//----------OSSConfig开始----------

using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.IRepository
{
    /// <summary>
    /// IOSSConfigRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 16:15:12 
    /// </summary>	
    public interface IOSSConfigRepository : IBaseRepository<OSSConfigModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<OSSConfigModel> SelectPage(OSSConfigParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 查询
        /// </summary>
        OSSConfigModel SelectOSSConfig(string Id);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertOSSConfig(OSSConfigModel oSSConfig);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateOSSConfig(OSSConfigModel oSSConfig);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteOSSConfig(string Id);
    }
}

//----------OSSConfig结束----------  
