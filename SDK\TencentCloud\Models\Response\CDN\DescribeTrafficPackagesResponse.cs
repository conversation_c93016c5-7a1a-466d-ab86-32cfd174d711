﻿namespace TencentCloud.Models.Response.CDN
{
    public class DescribeTrafficPackagesResponse
    {
        public DescribeTrafficPackagesDetailResponse Response { get; set; }
    }

    public class DescribeTrafficPackagesDetailResponse
    {
        public int EnabledCount { get; set; }
        public int ExpiringCount { get; set; }
        public int PaidCount { get; set; }
        public string RequestId { get; set; }
        public int TotalCount { get; set; }
        public List<Trafficpackage> TrafficPackages { get; set; }
    }

    public class Trafficpackage
    {
        public string Area { get; set; }
        public bool AutoExtension { get; set; }
        public long Bytes { get; set; }
        public long BytesUsed { get; set; }
        public string Channel { get; set; }
        public int ConfigId { get; set; }
        public bool ContractExtension { get; set; }
        public string CreateTime { get; set; }
        public string EnableTime { get; set; }
        public string ExpireTime { get; set; }
        public bool ExtensionAvailable { get; set; }
        public int ExtensionMode { get; set; }
        public int Id { get; set; }
        public int LifeTimeMonth { get; set; }
        public bool RefundAvailable { get; set; }
        public int Region { get; set; }
        public string Status { get; set; }
        public string TrueEnableTime { get; set; }
        public string TrueExpireTime { get; set; }
        public string Type { get; set; }
    }
}
