﻿using Quartz;
using YYBM.Cloud.Service.CDN.Tasks;
using ZProjectBase.Common.ITask;

namespace YYBM.WS.Cloud.Jobs
{
    public class FetchPackagesJob : IJob
    {
        private readonly FetchPackagesTask _fetchPackagesTask;
        private readonly ILogger<FetchPackagesJob> _logger;
        // 假设你的任务需要访问数据库
        // 通过构造函数注入依赖！
        public FetchPackagesJob(ILogger<FetchPackagesJob> logger, FetchPackagesTask fetchPackagesTask)
        {
            _logger = logger;
            _fetchPackagesTask = fetchPackagesTask;
        }
        public async Task Execute(IJobExecutionContext context)
        {
            _logger.LogInformation("FetchPackagesTaskJob starting at: {time}", DateTimeOffset.Now);
            try
            {
                await new TaskAsyncBase("FetchPackagesTask", _fetchPackagesTask).StartAsync();

                _logger.LogInformation("FetchPackagesTaskJob finished successfully at: {time}", DateTimeOffset.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while executing FetchPackagesTaskJob.");
                // 抛出 JobExecutionException 可以让 Quartz 知道任务失败，并根据配置进行重试等操作
                throw new JobExecutionException(ex);
            }
        }
    }
}
