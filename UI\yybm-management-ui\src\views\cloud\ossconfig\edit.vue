<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
    width="1000px"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="140px"
      size="medium"
      class="oss-config-form"
    >
      <el-row :gutter="16">
        <!-- 基本信息 -->
        <el-col :span="24">
          <div class="form-section-title">
            <i class="el-icon-setting"></i>
            基本配置
          </div>
        </el-col>

        <el-col :span="24">
          <el-form-item label="配置ID" prop="id">
            <el-input
              :disabled="isEdit"
              v-model="form.id"
              placeholder="新增时自动生成32位唯一ID"
              :class="{ 'id-input': true }"
            >
              <template slot="prepend">
                <i class="el-icon-key"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="配置名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入配置名称，用于标识该OSS配置"
              clearable
              maxlength="100"
              show-word-limit
            >
              <template slot="prepend">
                <i class="el-icon-edit-outline"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="所属项目" prop="projId">
            <x-select
              v-model="form.projId"
              url="/options/getProjectOptions"
              placeholder="选择所属项目"
              style="width: 100%"
              customRender
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="云服务平台" prop="platform">
            <x-select
              v-model="form.platform"
              url="/options/getCloudPlatformOptions"
              placeholder="选择云服务平台"
              style="width: 100%"
              customRender
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="域名OSS配置" prop="domainOssConfigId">
            <x-select
              v-model="form.domainOssConfigId"
              url="/options/getDomainOSSConfigOptions"
              placeholder="选择关联的域名OSS配置"
              style="width: 100%"
              customRender
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="启用状态" prop="enable">
            <div class="radio-group-container">
              <x-radio v-model="form.enable" button :options="enableOptions" />
            </div>
            <div class="form-tip">
              <i class="el-icon-info"></i>
              控制该OSS配置是否启用，禁用后将不会被系统使用
            </div>
          </el-form-item>
        </el-col>

        <!-- 路径配置 -->
        <el-col :span="24">
          <div class="form-section-title">
            <i class="el-icon-folder-opened"></i>
            路径配置
          </div>
        </el-col>

        <el-col :span="24">
          <el-form-item label="文件路径" prop="filesPath">
            <el-input
              type="textarea"
              :rows="4"
              v-model="form.filesPath"
              placeholder="请输入文件路径，多个路径使用换行分隔"
              class="config-textarea"
            />
            <div class="form-tip">
              <i class="el-icon-info"></i>
              文件夹路径请以 '/'
              结尾，多个路径请换行分隔，使用{文件路径}@{key[urlpath]}@{否生成域名}分隔
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="静态网站路径" prop="websiteFilePath">
            <el-input
              v-model="form.websiteFilePath"
              placeholder="请输入静态网站文件路径，如：/website/"
              clearable
            >
              <template slot="prepend">
                <i class="el-icon-link"></i>
              </template>
            </el-input>
            <div class="form-tip">
              <i class="el-icon-warning"></i>
              仅支持静态网站托管的云平台需要配置此项
            </div>
          </el-form-item>
        </el-col>

        <!-- 高级配置 -->
        <el-col :span="24">
          <div class="form-section-title">
            <i class="el-icon-s-tools"></i>
            高级配置
          </div>
        </el-col>

        <el-col :span="24">
          <el-form-item label="区域节点设置" prop="regionEndpointsConfig">
            <el-input
              type="textarea"
              :rows="6"
              v-model="form.regionEndpointsConfig"
              placeholder="请输入区域与服务器节点设置，格式：Endpoint@Location@Weight"
              class="config-textarea"
            />
            <div class="form-tip">
              <i class="el-icon-info"></i>
              格式：Endpoint@Location@Weight，多个配置换行分隔，系统将按权重随机选择
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="账号权重设置" prop="accountsWeightConfig">
            <el-input
              type="textarea"
              :rows="5"
              v-model="form.accountsWeightConfig"
              placeholder="请输入账号权重设置，格式：AccountId@Weight"
              class="config-textarea"
            />
            <div class="form-tip">
              <i class="el-icon-info"></i>
              格式：AccountId@Weight，多个账号换行分隔，系统将按权重随机选择账号
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { ossConfigApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      isEdit: false,
      form: {
        enable: true, // 默认启用
      },
      enableOptions: [
        { label: '启用', value: true },
        { label: '禁用', value: false },
      ],
      rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }],
        name: [{ required: true, message: '不能为空', trigger: 'blur' }],
        projId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        platform: [{ required: true, message: '不能为空', trigger: 'blur' }],
        domainOssConfigId: [{ required: true, message: '不能为空', trigger: 'blur' }],
        enable: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
        filesPath: [{ required: true, message: '不能为空', trigger: 'blur' }],
        regionEndpointsConfig: [{ required: true, message: '不能为空', trigger: 'blur' }],
        accountsWeightConfig: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
      this.isEdit = false
      // 新增时自动生成随机Id
      this.form.id = this.generateRandomId()
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.isEdit = true
      this.$xloading.show()
      const res = await ossConfigApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        enable: true, // 默认启用
      }
      this.$xResetForm('form')
    },

    // 生成随机Id（UUID v4，无连字符）
    generateRandomId() {
      if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
        const buf = new Uint8Array(16)
        window.crypto.getRandomValues(buf)
        buf[6] = (buf[6] & 0x0f) | 0x40
        buf[8] = (buf[8] & 0x3f) | 0x80
        const hex = Array.from(buf).map((b) => b.toString(16).padStart(2, '0'))
        // 返回不带“-”的32位十六进制字符串
        return hex.join('')
      }
      const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (Math.random() * 16) | 0
        const v = c === 'x' ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
      return uuid.replace(/-/g, '')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          if (this.isEdit) {
            const res = await ossConfigApi.editOSSConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await ossConfigApi.addOSSConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.oss-config-form {
  padding: 0 8px;
}

.form-section-title {
  display: flex;
  align-items: center;
  padding: 12px 0 16px 0;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #f0f0f0;
}

.form-section-title i {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.form-tip {
  display: flex;
  align-items: flex-start;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-left: 3px solid #409eff;
  border-radius: 4px;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.form-tip i {
  margin-right: 6px;
  margin-top: 1px;
  color: #409eff;
  font-size: 14px;
  flex-shrink: 0;
}

.config-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.id-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.radio-group-container {
  margin-bottom: 4px;
}

/* 输入框前置图标样式 */
.el-input-group__prepend {
  background: #fafafa;
  border-color: #dcdfe6;
  color: #909399;
}

/* 文本域样式优化 */
.config-textarea .el-textarea__inner {
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
  border-radius: 6px;
  border-color: #dcdfe6;
  transition: border-color 0.3s;
}

.config-textarea .el-textarea__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 表单项间距优化 */
.oss-config-form .el-form-item {
  margin-bottom: 22px;
}

/* 输入框样式优化 */
.oss-config-form .el-input__inner {
  border-radius: 6px;
  border-color: #dcdfe6;
  transition: all 0.3s;
}

.oss-config-form .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 选择框样式 */
.oss-config-form .el-select .el-input__inner {
  cursor: pointer;
}

/* 单选按钮组样式优化 */
.oss-config-form .el-radio-button__inner {
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: all 0.3s;
}

.oss-config-form .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background: #409eff;
  border-color: #409eff;
  box-shadow: -1px 0 0 0 #409eff;
}

/* 标签样式优化 */
.oss-config-form .el-form-item__label {
  font-weight: 500;
  color: #303133;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .form-section-title {
    font-size: 14px;
  }

  .oss-config-form {
    padding: 0;
  }
}
</style>
