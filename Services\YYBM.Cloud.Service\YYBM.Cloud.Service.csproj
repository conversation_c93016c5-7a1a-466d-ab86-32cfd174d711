﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="ObjectStorage\Management\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Project\YYLeagueSupplier\SDK\ZProjectBase.Common.Core.Net8\ZProjectBase.Common.Core.csproj" />
    <ProjectReference Include="..\..\Models\YYBM.Cloud.Entity\YYBM.Cloud.Entity.csproj" />
    <ProjectReference Include="..\..\Models\YYBM.Domain.Entity\YYBM.Domain.Entity.csproj" />
    <ProjectReference Include="..\..\Repositories\YYBM.Cloud.Repository\YYBM.Cloud.Repository.csproj" />
    <ProjectReference Include="..\..\Repositories\YYBM.Domain.Repository\YYBM.Domain.Repository.csproj" />
    <ProjectReference Include="..\..\SDK\Aliyun\Aliyun.csproj" />
    <ProjectReference Include="..\..\SDK\ECloud\ECloud.csproj" />
    <ProjectReference Include="..\..\SDK\TencentCloud\TencentCloud.csproj" />
  </ItemGroup>

</Project>
