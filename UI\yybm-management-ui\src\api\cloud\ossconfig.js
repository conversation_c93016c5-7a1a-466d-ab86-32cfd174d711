import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/oSSConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/oSSConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addOSSConfig(data) {
  return request({
    url: '/oSSConfig/add',
    method: 'post',
    data: data,
  })
}

export function editOSSConfig(data) {
  return request({
    url: '/oSSConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delOSSConfig(id) {
  return request({
    url: '/oSSConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------OSSConfig结束----------
