//----------Account开始----------

using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    /// <summary>
    /// AccountService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:28 
    /// </summary>	
    public class AccountService : AdminServiceBase<AccountModel>, IAccountService
    {

        private readonly IAccountRepository _accountRepository;

        public AccountService(IHttpContextAccessor httpContextAccessor, IAccountRepository accountRepository) : base(httpContextAccessor)
        {
            _accountRepository = accountRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<AccountVO> GetTableList(AccountParams searchParams)
        {
            var pageResult = _accountRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            return CreateTableResult<AccountModel, AccountVO>(pageResult);
        }


        /// <summary>
        /// 详情
        /// </summary>
        public AccountVO GetDetail(int Id)
        {
            var account = _accountRepository.Select(Id);
            if (account == null)
            {
                throw new BusinessException("数据不存在");
            }
            var accountVO = MapperIns.Map<AccountVO>(account);
            return accountVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddAccount(AccountParams accountParams)
        {
            var account = MapperIns.Map<AccountModel>(accountParams);
            account.CreateTime = DateTime.Now;
            var result = _accountRepository.InsertAccount(account);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateAccount(AccountParams accountParams)
        {
            var account = MapperIns.Map<AccountModel>(accountParams);
            account.EditTime = DateTime.Now;
            var result = _accountRepository.UpdateAccount(account);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteAccount(AccountParams accountParams)
        {
            CheckParamsUtils.Failure(accountParams.Id == 0, "Id不能为空");

            var result = _accountRepository.DeleteAccount(accountParams.Id);
            return ResponseResult.Result(result);
        }

    }
}

//----------Account结束----------
