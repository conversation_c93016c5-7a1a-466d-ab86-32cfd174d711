﻿using <PERSON>yun.OSS;
using <PERSON><PERSON>.OSS.Common;

namespace Aliyun.Apis.OSS
{
    public class ObjectApi : OSSBaseApi
    {
        public ObjectApi(string accessKeyId, string accessKeySecret, string region, string endpoint)
            : base(accessKeyId, accessKeySecret, region, endpoint)
        {
        }

        /// <summary>
        /// 查询指定存储空间下的所有对象
        /// </summary>
        /// <param name="bucketName"></param>
        /// <returns></returns>
        public List<OssObjectSummary>? ListObjects(string bucketName)
        {
            var listObjectsRequest = new ListObjectsRequest(bucketName);
            var objects = OSSClient.ListObjects(listObjectsRequest);
            return objects?.ObjectSummaries.ToList();
        }

        /// <summary>
        /// 查询指定存储空间下的指定对象
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public OssObject? GetObject(string bucketName, string key)
        {
            return OSSClient.GetObject(bucketName, key);
        }

        /// <summary>
        /// 上传文件到指定存储空间
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="key"></param>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public bool PutObjectFromFile(string bucketName, string key, string filePath)
        {
            PutObjectRequest putObjectRequest;

            using (Stream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                if (filePath.Contains(".html"))
                {
                    var objMeta = new ObjectMetadata()
                    {
                        ContentType = "*/*"
                    };
                    putObjectRequest = new PutObjectRequest(bucketName, key, fileStream, objMeta);
                }
                else
                {
                    putObjectRequest = new PutObjectRequest(bucketName, key, fileStream);
                }

                var res = OSSClient.PutObject(putObjectRequest);

                return !string.IsNullOrEmpty(res.ETag);
            }
        }

        /// <summary>
        /// 查询对象是否存在
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool DoesObjectExist(string bucketName, string key)
        {
            return OSSClient.DoesObjectExist(bucketName, key);
        }

        /// <summary>
        /// 删除指定存储空间下的指定对象
        /// </summary>
        /// <param name="bucketName"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        public bool DeleteObject(string bucketName, string key)
        {
            try
            {
                OSSClient.DeleteObject(bucketName, key);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting object: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 删除指定存储空间下的所有对象
        /// </summary>
        /// <param name="bucketName"></param>
        /// <returns></returns>
        public bool DeleteObjects(string bucketName)
        {
            try
            {
                var objects = OSSClient.ListObjects(bucketName);
                var keys = objects.ObjectSummaries?.Select(o => o.Key).ToList();
                if (keys == null || !keys.Any())
                {
                    Console.WriteLine("No objects to delete.");
                    return false;
                }

                var deleteObjectsRequest = new DeleteObjectsRequest(bucketName, keys, false);
                OSSClient.DeleteObjects(deleteObjectsRequest);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting object: {ex.Message}");
                return false;
            }
        }
    }
}
