//----------DomainOSSConfig开始----------

using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.Entity.VOs;
using YYBM.Domain.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    /// <summary>
    /// DomainOSSConfigService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:56 
    /// </summary>	
    public class DomainOSSConfigService : AdminServiceBase<DomainOSSConfigModel>, IDomainOSSConfigService
    {

        private readonly IDomainOSSConfigRepository _domainOSSConfigRepository;
        private readonly IDomainGroupRepository _domainGroupRepository;

        public DomainOSSConfigService(IHttpContextAccessor httpContextAccessor, IDomainOSSConfigRepository domainOSSConfigRepository, IDomainGroupRepository domainGroupRepository) : base(httpContextAccessor)
        {
            _domainOSSConfigRepository = domainOSSConfigRepository;
            _domainGroupRepository = domainGroupRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<DomainOSSConfigVO> GetTableList(DomainOSSConfigParams searchParams)
        {
            var pageResult = _domainOSSConfigRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            var tableResult = CreateTableResult<DomainOSSConfigModel, DomainOSSConfigVO>(pageResult);
            foreach (var item in tableResult.Data)
            {
                item.DomainGroupName = _domainGroupRepository.Select(item.DomainGroupId)?.Name ?? "未分组";
            }
            return tableResult;
        }


        /// <summary>
        /// 详情
        /// </summary>
        public DomainOSSConfigVO GetDetail(int Id)
        {
            var domainOSSConfig = _domainOSSConfigRepository.Select(Id);
            if (domainOSSConfig == null)
            {
                throw new BusinessException("数据不存在");
            }
            var domainOSSConfigVO = MapperIns.Map<DomainOSSConfigVO>(domainOSSConfig);
            return domainOSSConfigVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddDomainOSSConfig(DomainOSSConfigParams domainOSSConfigParams)
        {
            var domainOSSConfig = MapperIns.Map<DomainOSSConfigModel>(domainOSSConfigParams);
            domainOSSConfig.CreateOn = DateTime.Now;
            var result = _domainOSSConfigRepository.InsertDomainOSSConfig(domainOSSConfig);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateDomainOSSConfig(DomainOSSConfigParams domainOSSConfigParams)
        {
            var domainOSSConfig = MapperIns.Map<DomainOSSConfigModel>(domainOSSConfigParams);
            domainOSSConfig.EditTime = DateTime.Now;
            var result = _domainOSSConfigRepository.UpdateDomainOSSConfig(domainOSSConfig);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteDomainOSSConfig(DomainOSSConfigParams domainOSSConfigParams)
        {
            CheckParamsUtils.Failure(domainOSSConfigParams.Id == 0, "Id不能为空");

            var result = _domainOSSConfigRepository.DeleteDomainOSSConfig(domainOSSConfigParams.Id);
            return ResponseResult.Result(result);
        }

    }
}

//----------DomainOSSConfig结束----------
