import request from '@/utils/request'

// 查询列表
export function getList(params) {
  return request({
    url: '/system/menu/getList',
    method: 'get',
    params,
  })
}

export function getTreeData(isChildrenType) {
  return request({
    url: '/system/menu/treeData',
    method: 'get',
    params: {
      childrenType: isChildrenType,
    },
  })
}

export function getMenu(id) {
  return request({
    url: '/system/menu/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/system/menu/add',
    method: 'post',
    data: data,
  })
}

// 修改菜单
export function editMenu(data) {
  return request({
    url: '/system/menu/edit',
    method: 'post',
    data: data,
  })
}

// 删除菜单
export function delMenu(id) {
  return request({
    url: '/system/menu/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function queryMenu(menuName) {
  return request({
    url: '/system/menu/query',
    method: 'get',
    params: {
      menuName,
    },
  })
}
