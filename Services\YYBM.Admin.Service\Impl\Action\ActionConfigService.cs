﻿using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Common.Service.Caches.Action;
using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using YYBM.Entity.VOs;
using YYBM.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    public class ActionConfigService : AdminServiceBase<ActionConfigModel>, IActionConfigService
    {
        private readonly IActionConfigRepository _actionConfigRepository;

        public ActionConfigService(IHttpContextAccessor httpContextAccessor, IActionConfigRepository actionConfigRepository) : base(httpContextAccessor)
        {
            _actionConfigRepository = actionConfigRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<ActionConfigVO> GetTableList(ActionConfigParams searchParams)
        {
            var pageResult = _actionConfigRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            return CreateTableResult<ActionConfigModel, ActionConfigVO>(pageResult);
        }

        /// <summary>
        /// 查询
        /// </summary>
        public ActionConfigVO GetDetail(string ActionFlag)
        {
            var actionConfig = _actionConfigRepository.SelectActionConfig(ActionFlag);
            if (actionConfig == null)
            {
                throw new BusinessException("数据不存在");
            }
            var actionConfigVO = MapperIns.Map<ActionConfigVO>(actionConfig);
            return actionConfigVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddActionConfig(ActionConfigParams actionConfigParams)
        {
            var actionConfig = MapperIns.Map<ActionConfigModel>(actionConfigParams);
            actionConfig.CreateOn = DateTime.Now;
            var result = _actionConfigRepository.InsertActionConfig(actionConfig);
            ActionConfigCache.ClearCache();
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateActionConfig(ActionConfigParams actionConfigParams)
        {
            var actionConfig = MapperIns.Map<ActionConfigModel>(actionConfigParams);
            actionConfig.EditTime = DateTime.Now;
            var result = _actionConfigRepository.UpdateActionConfig(actionConfig);
            ActionConfigCache.ClearCache();
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteActionConfig(ActionConfigParams actionConfigParams)
        {
            CheckParamsUtils.Failure(string.IsNullOrEmpty(actionConfigParams.ActionFlag), "ActionFlag不能为空");

            var result = _actionConfigRepository.DeleteActionConfig(actionConfigParams.ActionFlag);
            ActionConfigCache.ClearCache();
            return ResponseResult.Result(result);
        }
    }
}
