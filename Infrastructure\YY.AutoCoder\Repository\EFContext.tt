<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude" #>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<#
    var outputPath = Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
    outputPath = Path.Combine(outputPath, OutputDllPath, "Temp", "EFContext");
    if (!Directory.Exists(outputPath)) Directory.CreateDirectory(outputPath);
#>

//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//     生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#>
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# var tableName=config.TableName; #>
<# if(tableName!=""){ #>
<# 
    // 生成单个表的DbContext
    var contextName = ProjectName + tableName + "Context";
    manager.StartBlock(contextName + ".cs", outputPath);
#>
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using <#=ProjectName#>.Entity.Models;
using ZProjectBase.DB.EF.Repository;

namespace <#=ProjectName#>.Repository.SqlDb
{
    /// <summary>
    /// <#=contextName#> - <#=tableName#>表的DbContext
    /// 此代码由T4模板自动生成
    /// 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#>
    /// </summary>
    public class <#=contextName#><T> : EFBaseRepository<T> where T : class, new()
    {
        public DbSet<<#=tableName#>Model> <#=tableName#>s { get; set; }

        public <#=contextName#>(DbContextOptions options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<<#=tableName#>Model>(entity =>
            {
                entity.ToTable("<#=tableName#>");
                <#
                    var primaryKeyFound = false;
                    foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName))
                    {
                        if(column.IsPrimaryKey) 
                        { 
                            primaryKeyFound = true;
                #>

                entity.HasKey(e => e.<#=column.ColumnName#>);
                <#
                            break;
                        }
                    }
                    if(!primaryKeyFound)
                    {
                #>

                entity.HasKey(e => e.Id);
                <#
                    }
                #>
            });
        }
    }
}
<# 
    manager.EndBlock();
#>
<# } else { #>
<#
    // 生成所有表的DbContext
    using(SqlConnection conn = new SqlConnection(config.ConnectionString)){
        conn.Open();
        var schema = conn.GetSchema("TABLES");
        
        // 首先生成一个包含所有表的主DbContext
        var mainContextName = ProjectName + "MainContext";
        manager.StartBlock(mainContextName + ".cs", outputPath);
#>
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using <#=ProjectName#>.Entity.Models;
using ZProjectBase.DB.EF.Repository;

namespace <#=ProjectName#>.Repository.SqlDb
{
    /// <summary>
    /// <#=mainContextName#> - 主DbContext，包含所有表
    /// 此代码由T4模板自动生成
    /// 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#>
    /// </summary>
    public class <#=mainContextName#><T> : EFBaseRepository<T> where T : class, new()
    {
<#
        // 为每个表生成DbSet属性
        foreach(DataRow row in schema.Rows){
            var tableNameStr = row["TABLE_NAME"].ToString();
#>
        public DbSet<<#=tableNameStr#>Model> <#=tableNameStr#>s { get; set; }
<#
        }
#>

        public <#=mainContextName#>(DbContextOptions options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

<#
        // 为每个表配置实体映射
        foreach(DataRow row in schema.Rows){
            var tableNameStr = row["TABLE_NAME"].ToString();
#>
            modelBuilder.Entity<<#=tableNameStr#>Model>(entity =>
            {
                entity.ToTable("<#=tableNameStr#>");
                <#
                    var primaryKeyFound = false;
                    foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableNameStr))
                    {
                        if(column.IsPrimaryKey) 
                        { 
                            primaryKeyFound = true;
                #>

                entity.HasKey(e => e.<#=column.ColumnName#>);
                <#
                            break;
                        }
                    }
                    if(!primaryKeyFound)
                    {
                #>

                entity.HasKey(e => e.Id);
                <#
                    }
                #>
            });

<#
        }
#>
        }
    }
}

//----------<#=mainContextName#>结束----------
<#
        manager.EndBlock();
        
        // 然后为每个表生成单独的DbContext
        foreach(DataRow row in schema.Rows){
            var tableNameStr = row["TABLE_NAME"].ToString();
            var contextName = ProjectName + tableNameStr + "Context";
            manager.StartBlock(contextName + ".cs", outputPath);
#>
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using <#=ProjectName#>.Entity.Models;
using ZProjectBase.DB.EF.Repository;

namespace <#=ProjectName#>.Repository.SqlDb
{
    /// <summary>
    /// <#=contextName#> - <#=tableNameStr#>表的DbContext
    /// 此代码由T4模板自动生成
    /// 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#>
    /// </summary>
    public class <#=contextName#><T> : EFBaseRepository<T> where T : class, new()
    {
        public DbSet<<#=tableNameStr#>Model> <#=tableNameStr#>s { get; set; }

        public <#=contextName#>(DbContextOptions options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<<#=tableNameStr#>Model>(entity =>
            {
                entity.ToTable("<#=tableNameStr#>");
                <#
                    var primaryKeyFound = false;
                    foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableNameStr))
                    {
                        if(column.IsPrimaryKey) 
                        { 
                            primaryKeyFound = true;
                #>

                entity.HasKey(e => e.<#=column.ColumnName#>);
                <#
                            break;
                        }
                    }
                    if(!primaryKeyFound)
                    {
                #>

                entity.HasKey(e => e.Id);
                <#
                    }
                #>
            });
        }
    }
}

//----------<#=tableNameStr#>结束----------
<#
            manager.EndBlock();
        }
        manager.Process(true);
    }
#>
<# } #>
