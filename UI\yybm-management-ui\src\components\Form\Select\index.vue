<template>
  <el-select v-bind="$attrs" v-on="$listeners" :value="currentValue" @change="handleChange">
    <slot v-bind:options="optionData">
      <template v-if="group">
        <el-option-group v-for="group in optionData" :key="group.label" :label="group.label">
          <el-option
            v-for="item in group.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <template v-if="customRender">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; font-size: 13px" :style="{ color: item.color }">{{
                item.description
              }}</span>
            </template>
          </el-option>
        </el-option-group>
      </template>
      <template v-else>
        <el-option
          v-for="item in optionData"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <template v-if="customRender">
            <span style="float: left">{{ item.label }}</span>
            <span style="float: right; font-size: 13px" :style="{ color: item.color }">{{
              item.description
            }}</span>
          </template>
        </el-option>
      </template>
    </slot>
  </el-select>
</template>

<script>
import request from '@/utils/request'
export default {
  inheritAttrs: false,
  props: {
    showDefault: {
      // 是否显示默认的选项（全部）
      type: Boolean,
      default: false,
    },
    options: Array, // 选项的数据，对象的属性为 { label: "xxx", value: xxx }
    url: String, // url，对象属性同上
    value: {
      type: [String, Number, Boolean],
    },
    customRender: {
      type: Boolean,
      default: false,
    },
    group: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      optionData: [],
      currentValue: this.value,
    }
  },
  mounted() {
    // console.log(this.url)
    if (this.options) {
      this.updateOptionsData(this.options)
    }
    if (this.url) {
      this.loadOptions(this.url)
    }
  },
  watch: {
    options(newVal) {
      this.updateOptionsData(newVal)
    },
    // eslint-disable-next-line no-unused-vars
    url(newVal) {
      // console.log(newVal)
      this.loadOptions(this.url)
    },
    value(newVal) {
      // console.log(newVal)
      this.setCurrentValue(newVal)
    },
  },
  methods: {
    async loadOptions(url) {
      const res = await request({
        url: url,
        method: 'get',
      })
      if (res.code == 0) {
        this.updateOptionsData(res.data)
      } else {
        console.error(`select组件获取数据失败【${url}】：${res.msg}`)
      }
    },
    handleChange(value) {
      // console.log('handleInput:', value)
      this.setCurrentValue(value)
      this.$emit('input', value)
    },
    setCurrentValue(value) {
      if (value == this.currentValue) {
        return
      }
      this.currentValue = value
    },
    updateOptionsData(options) {
      if (this.showDefault) {
        var defaultOptionItem = {
          label: '全部',
          value: -1,
          options: [{ label: '全部', value: -1 }], //兼容group
        }
        this.optionData = [defaultOptionItem, ...options]
        return
      }
      this.optionData = options
    },
  },
}
</script>

<style></style>
