﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Domain
{
    [Route("api/[controller]")]
    [ApiController]
    public class DomainOSSConfigController : BaseApiController
    {
        private readonly IDomainOSSConfigService _domainOSSConfigService;

        public DomainOSSConfigController(IDomainOSSConfigService domainOSSConfigService)
        {
            _domainOSSConfigService = domainOSSConfigService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("domainOSSConfig:list")]
        public TableResult<DomainOSSConfigVO> GetList([FromQuery] DomainOSSConfigParams searchParams)
        {
            return _domainOSSConfigService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("domainOSSConfig:detail")]
        public ResponseResult<DomainOSSConfigVO> GetDetail([FromQuery] int id)
        {
            return new ResponseResult<DomainOSSConfigVO>(_domainOSSConfigService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("domainOSSConfig:add")]
        public ResponseResult Add([FromBody] DomainOSSConfigParams domainParams)
        {
            return _domainOSSConfigService.AddDomainOSSConfig(domainParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("domainOSSConfig:edit")]
        public ResponseResult Edit([FromBody] DomainOSSConfigParams domainParams)
        {
            return _domainOSSConfigService.UpdateDomainOSSConfig(domainParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("domainOSSConfig:delete")]
        public ResponseResult Delete([FromBody] DomainOSSConfigParams domainParams)
        {
            return _domainOSSConfigService.DeleteDomainOSSConfig(domainParams);
        }
    }
}
