//----------Domain开始----------

using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// DomainService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:55 
    /// </summary>	
    public interface IDomainService : IBaseService<DomainModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<DomainVO> GetTableList(DomainParams searchParams);

        /// <summary>
        /// 详情
        /// </summary>
        DomainVO GetDetail(int Id);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddDomain(DomainParams domainParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateDomain(DomainParams domainParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteDomain(DomainParams domainParams);

    }
}

//----------Domain结束----------
