﻿using YYBM.Cloud.Entity.Models;
using ZProjectBase.Common.Extend;

namespace YYBM.Cloud.Service.ObjectStorage.Mappers
{
    public class FilesPathConfigMapper
    {
        public static List<FilesPathConfig> MapToFilesPathConfigList(OSSConfigModel oSSConfig)
        {
            var filesPath = oSSConfig.FilesPath;
            if (string.IsNullOrWhiteSpace(filesPath))
            {
                throw new ArgumentException("FilesPath cannot be null or empty.");
            }
            var filesPathConfigs = new List<FilesPathConfig>();
            var paths = filesPath.Splitline();
            foreach (var item in paths)
            {
                if (item.StartsWith("--"))
                    continue;

                var parts = item.Split('@');
                var path = parts[0].Trim();
                var isDirectory = path.EndsWith("\\");
                var key = parts.Length >= 2 ? parts[1].Trim() : string.Empty;
                string? urlPath = null;
                if (key.Contains("["))
                {
                    urlPath = key.Substring(key.IndexOf("[") + 1, key.IndexOf("]") - key.IndexOf("[") - 1);
                    key = key.Substring(0, key.IndexOf("[")).Trim();
                }
                var isCreateDomain = parts.Length >= 3 && parts[2].Trim().ToInt() == 1;
                filesPathConfigs.Add(new FilesPathConfig
                {
                    Path = path,
                    IsDirectory = isDirectory,
                    Key = key,
                    IsCreateDomain = isCreateDomain,
                    UrlPath = urlPath
                });
            }
            return filesPathConfigs;
        }
    }

    public class FilesPathConfig
    {
        public string Path { get; set; }
        public bool IsDirectory { get; set; }
        public string Key { get; set; }
        public bool IsCreateDomain { get; set; }
        public string? UrlPath { get; set; }
    }
}
