﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Domain
{
    [Route("api/[controller]")]
    [ApiController]
    public class DomainController : BaseApiController
    {
        private readonly IDomainService _domainService;
        public DomainController(IDomainService domainService) : base()
        {
            _domainService = domainService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("domain:list")]
        public TableResult<DomainVO> GetList([FromQuery] DomainParams searchParams)
        {
            return _domainService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("domain:detail")]
        public ResponseResult<DomainVO> GetDetail([FromQuery] int id)
        {
            return new ResponseResult<DomainVO>(_domainService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("domain:add")]
        public ResponseResult Add([FromBody] DomainParams domainParams)
        {
            return _domainService.AddDomain(domainParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("domain:edit")]
        public ResponseResult Edit([FromBody] DomainParams domainParams)
        {
            return _domainService.UpdateDomain(domainParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("domain:delete")]
        public ResponseResult Delete([FromBody] DomainParams domainParams)
        {
            return _domainService.DeleteDomain(domainParams);
        }
    }
}
