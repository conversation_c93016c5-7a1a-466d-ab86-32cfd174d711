import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/domainOSSConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/domainOSSConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDomainOSSConfig(data) {
  return request({
    url: '/domainOSSConfig/add',
    method: 'post',
    data: data,
  })
}

export function editDomainOSSConfig(data) {
  return request({
    url: '/domainOSSConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delDomainOSSConfig(id) {
  return request({
    url: '/domainOSSConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------DomainOSSConfig结束----------
