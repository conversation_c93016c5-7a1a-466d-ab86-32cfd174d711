using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Domain.Entity.Models
{
    ///<summary>
    ///DomainGroup
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-15 19:38:49 
    ///</summary>
    [Table("DomainGroup")]
    public partial class DomainGroupModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(false)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 分组名称
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 分组描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        [Required]
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

    }
}

