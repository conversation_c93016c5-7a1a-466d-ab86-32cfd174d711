﻿<?xml version="1.0" encoding="utf-8" ?>
<log4net>

	<!--
  根日志器 (Root Logger) 配置
  负责定义所有未显式配置的Logger的默认行为。
  本例中，所有日志信息必须至少达到 INFO 级别才会被处理。
  -->
	<root>
		<!-- 设置根日志器级别为 INFO。
         这意味着只有 INFO, WARN, ERROR, FATAL 级别的日志才会在这里被捕获和处理。
         低于 INFO (如 DEBUG, ALL) 的日志将不会向下传播。
    -->
		<level value="INFO" />

		<!--
    这里引用你希望所有 INFO 及以上日志都流向的 Appender。
    在本例中，我们将所有日志级别（INFO, WARN, ERROR, FATAL）都导向到各自的特定文件。
    请注意，每个 Appender 内部也会有自己的过滤器，以确保只记录特定级别。
    -->
		<appender-ref ref="TastInfo" />
		<appender-ref ref="TastWarn" />
		<appender-ref ref="TastError" />
		<appender-ref ref="TastFatal" />
		<!-- TastDebug 在此配置下，将不会收到任何日志，因为 root 级别是 INFO -->
		<appender-ref ref="TastDebug" />
	</root>


	<!-- Appender for INFO level logs -->
	<appender name="TastInfo" type="log4net.Appender.RollingFileAppender">
		<!-- 日志文件路径。请确保应用程序有写入这些目录的权限。 -->
		<file value="Log\\Info\\" />
		<lockingModel type="log4net.Appender.FileAppender+InterProcessLock"/>
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<!-- 备份文件总数 (即保留最新的30天日志) -->
		<maxSizeRollBackups value="30" />
		<!-- 按日期滚动时，此项不再需要，即使保留也不会有负面影响 -->
		<maximumFileSize value="20MB" />
		<!-- staticLogFileName="false" 意味着日志文件名会根据 DatePattern 动态生成，例如 Log\Info\2023-11-20.log -->
		<staticLogFileName value="false" />
		<!-- 日期模式，确保文件名中包含日期，例如 2023-11-20.log -->
		<DatePattern value="yyyy-MM-dd'.log'"/>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date  %-5level  - %message%newline" />
		</layout>
		<!--
    过滤器：只允许 INFO 级别的日志通过此 Appender。
    这意味着即使上层 Logger 传递了 WARN, ERROR 等日志，此 Appender 也只会记录 INFO 级别的。
    -->
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="INFO" />
			<levelMax value="INFO" />
		</filter>
	</appender>

	<!-- Appender for ERROR level logs -->
	<appender name="TastError" type="log4net.Appender.RollingFileAppender">
		<file value="Log\\Error\\" />
		<lockingModel type="log4net.Appender.FileAppender+InterProcessLock"/>
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<maxSizeRollBackups value="30" />
		<maximumFileSize value="20MB" />
		<staticLogFileName value="false" />
		<DatePattern value="yyyy-MM-dd'.log'"/>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date  %-5level - %message%newline" />
		</layout>
		<!-- 过滤器：只允许 ERROR 级别的日志通过此 Appender。 -->
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="ERROR" />
			<levelMax value="ERROR" />
		</filter>
	</appender>

	<!-- Appender for DEBUG level logs (将不会收到日志，因为根Logger级别为INFO) -->
	<appender name="TastDebug" type="log4net.Appender.RollingFileAppender">
		<file value="Log\\Debug\\" />
		<lockingModel type="log4net.Appender.FileAppender+InterProcessLock"/>
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<maxSizeRollBackups value="30" />
		<maximumFileSize value="100MB" />
		<staticLogFileName value="false" />
		<DatePattern value="yyyy-MM-dd'.log'"/>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date  %-5level - %message%newline" />
		</layout>
		<!-- 过滤器：只允许 DEBUG 级别的日志通过此 Appender。 -->
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="DEBUG" />
			<levelMax value="DEBUG" />
		</filter>
	</appender>

	<!-- Appender for FATAL level logs -->
	<appender name="TastFatal" type="log4net.Appender.RollingFileAppender">
		<file value="Log\\Fatal\\" />
		<lockingModel type="log4net.Appender.FileAppender+InterProcessLock"/>
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<maxSizeRollBackups value="30" />
		<maximumFileSize value="20MB" />
		<staticLogFileName value="false" />
		<DatePattern value="yyyy-MM-dd'.log'"/>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date  %-5level - %message%newline" />
		</layout>
		<!-- 过滤器：只允许 FATAL 级别的日志通过此 Appender。 -->
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="FATAL" />
			<levelMax value="FATAL" />
		</filter>
	</appender>

	<!-- Appender for WARN level logs -->
	<appender name="TastWarn" type="log4net.Appender.RollingFileAppender">
		<file value="Log\\Warn\\" />
		<lockingModel type="log4net.Appender.FileAppender+InterProcessLock"/>
		<appendToFile value="true" />
		<rollingStyle value="Date" />
		<maxSizeRollBackups value="30" />
		<maximumFileSize value="20MB" />
		<staticLogFileName value="false" />
		<DatePattern value="yyyy-MM-dd'.log'"/>
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date  %-5level - %message%newline" />
		</layout>
		<!-- 过滤器：只允许 WARN 级别的日志通过此 Appender。 -->
		<filter type="log4net.Filter.LevelRangeFilter">
			<levelMin value="WARN" />
			<levelMax value="WARN" />
		</filter>
	</appender>

	<!--
  移除原先的具名Logger定义：
  如果你希望使用特定的Logger名称来获取日志实例，例如 `LogManager.GetLogger("CustomInfo")`，
  并且希望这个Logger有它自己的特定行为，你可以在这里定义。
  但这通常不如在 root 上设置全局级别，并通过 appender 过滤器分发日志来得通用和高效。

  例如，如果你仍想使用 `log4net.LogManager.GetLogger("Info")` 并且让它只写入到 TastInfo:
  <logger name="Info">
    <level value="INFO"/>
    <appender-ref ref="TastInfo" />
  </logger>
  但请注意，如果 `root` 已经引用了 `TastInfo`，这可能会导致重复写入，除非 `additivity="false"`。
  为了简洁和符合“只显示 Info 以上级别”的整体目标，上述配置已足够。
  我们推荐直接使用根 logger（通过 `LogManager.GetLogger(typeof(YourClass))` 获得会自动继承根配置）
  然后让它通过 `root` 引用所有的 appender，并由 appender 自身的过滤器进行精细控制。
  -->

</log4net>
