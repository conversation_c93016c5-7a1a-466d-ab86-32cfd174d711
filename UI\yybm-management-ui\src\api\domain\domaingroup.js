import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/domainGroup/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/domainGroup/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDomainGroup(data) {
  return request({
    url: '/domainGroup/add',
    method: 'post',
    data: data,
  })
}

export function editDomainGroup(data) {
  return request({
    url: '/domainGroup/edit',
    method: 'post',
    data: data,
  })
}

export function delDomainGroup(id) {
  return request({
    url: '/domainGroup/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------DomainGroup结束----------
