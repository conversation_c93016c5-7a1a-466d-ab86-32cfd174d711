<template>
  <el-row :gutter="20" class="panel-group">
    <el-col
      v-for="(item, index) in list"
      :key="index"
      :xs="24"
      :sm="12"
      :lg="8"
      class="card-panel-col"
    >
      <div class="card-panel">
        <div class="card-header" :title="item.title">{{ item.title }}</div>
        <div class="card-main" :title="item.text">{{ item.text }}</div>
        <div class="card-sub" v-if="item.subText" :title="item.subText">{{ item.subText }}</div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: 'PanelGroup',
  props: {
    // 接收上层传入的数据，可能在接口未返回前不是数组，这里做兼容
    data: {
      type: [Array, Object],
      required: true,
    },
  },
  computed: {
    // 始终返回数组，避免渲染阶段类型不一致导致的警告或错误
    list() {
      return Array.isArray(this.data) ? this.data : []
    },
  },
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 20px;
  }

  .card-panel {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 18px 20px;
    min-height: 110px;
    background: #fff;
    color: #303133;
    border-radius: 12px;
    border: 1px solid #f0f2f5;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 24px rgba(0, 0, 0, 0.08);
      border-color: #e6e8eb;
    }

    .card-header {
      font-size: 14px;
      line-height: 1.2;
      color: rgba(0, 0, 0, 0.55);
      font-weight: 600;
      letter-spacing: 0.2px;
    }

    .card-main {
      margin-top: 8px;
      font-size: 22px;
      line-height: 1.3;
      font-weight: 700;
      color: #1f2d3d;
      word-break: break-all;
    }

    .card-sub {
      margin-top: 6px;
      font-size: 12px;
      line-height: 1.3;
      color: #909399;
      word-break: break-all;
    }
  }
}

@media (max-width: 550px) {
  .panel-group {
    .card-panel-col {
      margin-bottom: 16px;
    }

    .card-panel {
      padding: 16px;
      min-height: 96px;
    }
  }
}
</style>
