//----------DomainOSSConfig开始----------   

using Microsoft.Extensions.Configuration;
using System.Text;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.IRepository;
using YYBM.Domain.Repository.SqlDb;
using ZProjectBase.DB.Extend;
using ZProjectBase.Mvc;

namespace YYBM.Domain.Repository
{
    /// <summary>
    /// DomainOSSConfigRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:53 
    /// </summary>	
    public class DomainOSSConfigRepository : YYBMDomainRepository<DomainOSSConfigModel>, IDomainOSSConfigRepository
    {
        public DomainOSSConfigRepository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<DomainOSSConfigModel> SelectPage(DomainOSSConfigParams searchParams, PageInfo pageInfo)
        {
            var whereSql = new StringBuilder();
            whereSql.AppendSqlIf("and Id = @Id", searchParams.Id > 0);
            whereSql.AppendSqlIf("and Name = @Name", searchParams.Name.IsNotNullOrEmpty());

            var orderBy = "order by CreateOn desc";

            var pageResult = new PageResult<DomainOSSConfigModel>();
            long total;
            pageResult.Data = conn.GetByPage<DomainOSSConfigModel>(pageInfo.page, pageInfo.limit, out total, param: searchParams, where: whereSql.ToWhereSql(), orderBy: orderBy);
            pageResult.Count = total;
            return pageResult;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public bool InsertDomainOSSConfig(DomainOSSConfigModel domainOSSConfig)
        {
            var res = conn.Insert(domainOSSConfig) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool UpdateDomainOSSConfig(DomainOSSConfigModel domainOSSConfig)
        {
            return conn.UpdateById(domainOSSConfig) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool DeleteDomainOSSConfig(int Id)
        {
            return conn.DeleteById<DomainOSSConfigModel>(Id) > 0;
        }
    }
}

//----------DomainOSSConfig结束----------

