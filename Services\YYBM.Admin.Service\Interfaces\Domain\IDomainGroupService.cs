//----------DomainGroup开始----------

using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// DomainGroupService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:55 
    /// </summary>	
    public interface IDomainGroupService : IBaseService<DomainGroupModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<DomainGroupVO> GetTableList(DomainGroupParams searchParams);

        /// <summary>
        /// 详情
        /// </summary>
        DomainGroupVO GetDetail(int Id);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddDomainGroup(DomainGroupParams domainGroupParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateDomainGroup(DomainGroupParams domainGroupParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteDomainGroup(DomainGroupParams domainGroupParams);

    }
}

//----------DomainGroup结束----------
