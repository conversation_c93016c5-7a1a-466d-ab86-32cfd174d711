<template>
  <div class="app-container">
    <panel-group :data="cardDataList" />
  </div>
</template>

<script>
import PanelGroup from './components/PanelGroup'
import { dashboardApi } from '@/api'
export default {
  name: 'DashboardAdmin',
  components: {
    PanelGroup,
  },
  data() {
    return {
      queryParams: {},
      cardDataList: {},
    }
  },
  async created() {
    this.$xloading.show()
    await this.getDatas()
    this.$xloading.hide()
  },
  methods: {
    async getDatas() {
      var res = await dashboardApi.getCardList()
      if (res.code == 0) {
        this.cardDataList = res.data || {}
      }
    },
  },
}
</script>
