<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="visible = false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="上级菜单" prop="parentId">
            <treeselect v-model="form.parentId" :options="parentMenus" placeholder="选择上级菜单" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="菜单类型" prop="menuType">
            <el-radio-group v-model="form.menuType">
              <el-radio :label="0">目录</el-radio>
              <el-radio :label="1">菜单</el-radio>
              <el-radio :label="2">按钮</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :xs="formItemCol.xs" :md="formItemCol.md">
          <el-form-item label="菜单名称" prop="menuName">
            <el-input v-model="form.menuName" placeholder="请输入菜单名称" />
          </el-form-item>
        </el-col>
        <el-col :xs="formItemCol.xs" :md="formItemCol.md">
          <el-form-item label="显示排序" prop="sort">
            <el-input-number v-model="form.sort" controls-position="right" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.menuType != '2'">
          <el-form-item label="菜单图标" prop="icon">
            <el-input v-model="form.icon" placeholder="请输入图标类名">
              <template slot="prepend">
                <x-icon :icon="form.icon" />
              </template>
              <el-button slot="append" icon="el-icon-thumb" @click="openIconsDialog"></el-button>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.menuType != '2'">
          <el-form-item label="路由地址" prop="path">
            <el-input v-model="form.path" placeholder="请输入路由地址" />
          </el-form-item>
        </el-col>
        <el-col :xs="formItemCol.xs" :md="formItemCol.md" v-if="form.menuType == '1'">
          <el-form-item label="组件路径" prop="component">
            <el-input v-model="form.component" placeholder="请输入组件路径" />
          </el-form-item>
        </el-col>
        <el-col :xs="formItemCol.xs" :md="formItemCol.md">
          <el-form-item v-if="form.menuType != '0'" label="权限标识" prop="permission">
            <el-input v-model="form.permission" placeholder="请权限标识" maxlength="50" />
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.menuType != '2'">
          <el-form-item label="菜单状态" prop="isShow">
            <el-radio-group v-model="form.isShow">
              <el-radio :label="true">显示</el-radio>
              <el-radio :label="false">隐藏</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <icons-dialog :visible.sync="iconsDialogVisible" :current="form.icon" @select="onIconSelect" />
  </x-dialog>
</template>

<script>
import { mixinDevice } from '@/utils/mixin'
import * as menuApi from '@/api/system/menu'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import IconsDialog from '@/components/IconsDialog'

export default {
  components: {
    Treeselect,
    IconsDialog,
  },
  mixins: [mixinDevice],
  data() {
    return {
      formItemCol: {
        xs: 24,
        md: 12,
      },
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        menuType: [{ required: true, message: '不能为空', trigger: 'blur' }],
        menuName: [{ required: true, message: '不能为空', trigger: 'blur' }],
        sort: [{ required: true, message: '不能为空', trigger: 'blur' }],
        path: [{ required: true, message: '不能为空', trigger: 'blur' }],
        isShow: [{ required: true, message: '不能为空', trigger: 'blur' }],
        systemCode: [{ required: true, message: '不能为空', trigger: 'blur' }],
      },
      parentMenus: [],
      iconsDialogVisible: false,
    }
  },
  async created() {},
  mounted() {},

  methods: {
    async add(row) {
      this.reset()
      this.title = '新增'
      this.visible = true
      await this.getTreeData()
      if (row) {
        this.$set(this.form, 'parentId', row.id)
      }
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      await this.getTreeData()
      const res = await menuApi.getMenu(row.id)
      if (res.code == 0) {
        this.form = res.data
        this.$xloading.hide()
      }
    },
    reset() {
      this.form = {
        // id: undefined,
        // parentId: 0,
        // menuName: undefined,
        // icon: undefined,
        // menuType: 0,
        // menuSort: undefined,
        isShow: true,
      }
      this.$xResetForm('form')
    },
    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          this.$xloading.show()
          if (this.form.id != undefined) {
            const res = await menuApi.editMenu(this.form)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await menuApi.addMenu(this.form)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },

    async getTreeData() {
      const res = await menuApi.getTreeData(true)
      this.parentMenus = res.data
    },

    openIconsDialog() {
      this.iconsDialogVisible = true
    },
    onIconSelect(icon) {
      this.form.icon = icon
    },
  },
}
</script>

<style></style>
