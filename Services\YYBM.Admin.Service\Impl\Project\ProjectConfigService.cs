//----------ProjectConfig开始----------

using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using YYBM.Entity.VOs;
using YYBM.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    /// <summary>
    /// ProjectConfigService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 18:12:15 
    /// </summary>	
    public class ProjectConfigService : AdminServiceBase<ProjectConfigModel>, IProjectConfigService
    {

        private readonly IProjectConfigRepository _projectConfigRepository;

        public ProjectConfigService(IHttpContextAccessor httpContextAccessor, IProjectConfigRepository projectConfigRepository) : base(httpContextAccessor)
        {
            _projectConfigRepository = projectConfigRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<ProjectConfigVO> GetTableList(ProjectConfigParams searchParams)
        {
            var pageResult = _projectConfigRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            return CreateTableResult<ProjectConfigModel, ProjectConfigVO>(pageResult);
        }


        /// <summary>
        /// 详情
        /// </summary>
        public ProjectConfigVO GetDetail(int Id)
        {
            var projectConfig = _projectConfigRepository.Select(Id);
            if (projectConfig == null)
            {
                throw new BusinessException("数据不存在");
            }
            var projectConfigVO = MapperIns.Map<ProjectConfigVO>(projectConfig);
            return projectConfigVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddProjectConfig(ProjectConfigParams projectConfigParams)
        {
            var projectConfig = MapperIns.Map<ProjectConfigModel>(projectConfigParams);
            projectConfig.CreateOn = DateTime.Now;
            var result = _projectConfigRepository.InsertProjectConfig(projectConfig);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateProjectConfig(ProjectConfigParams projectConfigParams)
        {
            var projectConfig = MapperIns.Map<ProjectConfigModel>(projectConfigParams);
            projectConfig.EditTime = DateTime.Now;
            var result = _projectConfigRepository.UpdateProjectConfig(projectConfig);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteProjectConfig(ProjectConfigParams projectConfigParams)
        {
            CheckParamsUtils.Failure(projectConfigParams.Id == 0, "Id不能为空");

            var result = _projectConfigRepository.DeleteProjectConfig(projectConfigParams.Id.Value);
            return ResponseResult.Result(result);
        }
    }
}

//----------ProjectConfig结束----------
