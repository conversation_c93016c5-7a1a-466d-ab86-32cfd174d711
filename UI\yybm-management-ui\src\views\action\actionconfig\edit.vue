<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="标识" prop="actionFlag">
            <el-input v-model="form.actionFlag" placeholder="请输入操作标识" :disabled="isEdit" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入操作标题" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="值类型" prop="type">
            <x-radio v-model="form.type" button :options="actionTypeOptions" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操作值" prop="value">
            <el-card shadow="never" style="border-radius: 8px; padding: 10px">
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 10px;
                "
              >
                <span style="font-weight: bold; font-size: 14px">{{
                  showJsonEditor ? 'JSON编辑器' : '文本编辑器'
                }}</span>
                <div>
                  <el-button
                    type="primary"
                    size="small"
                    @click="formatJson"
                    :disabled="!showJsonEditor"
                    style="margin-right: 8px"
                  >
                    格式化JSON
                  </el-button>
                  <el-button
                    type="success"
                    size="small"
                    @click="validateJson"
                    :disabled="!showJsonEditor"
                    style="margin-right: 8px"
                  >
                    验证JSON
                  </el-button>
                  <el-button type="info" size="small" @click="toggleEditor">
                    {{ showJsonEditor ? '切换到文本模式' : '切换到JSON模式' }}
                  </el-button>
                </div>
              </div>

              <!-- JSON编辑器模式 -->
              <div v-if="showJsonEditor">
                <el-input
                  v-model="jsonString"
                  type="textarea"
                  :autosize="{ minRows: 8, maxRows: 20 }"
                  placeholder="请输入JSON格式的操作值"
                  class="json-editor"
                  @input="onJsonInput"
                />
                <div v-if="jsonError" class="json-error">
                  <i class="el-icon-warning"></i>
                  {{ jsonError }}
                </div>
                <div v-if="jsonValid" class="json-success">
                  <i class="el-icon-success"></i>
                  JSON格式正确
                </div>
              </div>

              <!-- 普通文本编辑器模式 -->
              <el-input
                v-else
                v-model="form.value"
                type="textarea"
                :autosize="{ minRows: 8, maxRows: 20 }"
                placeholder="请输入操作值"
                class="text-editor"
              />
            </el-card>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              type="textarea"
              v-model="form.description"
              placeholder="请输入描述"
              rows="4"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否启用" prop="enable">
            <x-radio v-model="form.enable" button :options="enableOptions" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { actionTypeOptions } from '@/utils/options'
import { actionConfigApi } from '@/api'
import { enableOptions } from '@/utils/options'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      isEdit: false,
      form: {
        enable: true,
      },
      enableOptions,
      showJsonEditor: false, // 控制是否显示JSON编辑器
      jsonString: '', // JSON字符串
      jsonError: '', // JSON错误信息
      jsonValid: false, // JSON是否有效
      actionTypeOptions,
    }
  },
  watch: {
    // 监听form.value变化，同步到jsonString
    'form.value': {
      handler(newVal) {
        if (this.showJsonEditor && newVal !== this.jsonString) {
          this.jsonString = newVal
          this.validateJsonString()
        }
      },
      immediate: true,
    },
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
      this.isEdit = false
      this.initJsonEditor()
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.isEdit = true
      this.$xloading.show()
      const res = await actionConfigApi.getDetail(row.actionFlag)
      if (res.code == 0) {
        this.form = res.data
        this.initJsonEditor()
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        enable: true,
      }
      this.jsonString = ''
      this.jsonError = ''
      this.jsonValid = false
      this.showJsonEditor = false
      this.$xResetForm('form')
    },

    // 初始化JSON编辑器
    initJsonEditor() {
      if (this.form.value) {
        this.jsonString = this.form.value
        this.detectJsonMode()
      }
    },

    // 自动检测是否为JSON格式
    detectJsonMode() {
      try {
        if (this.form.value && this.form.value.trim()) {
          JSON.parse(this.form.value)
          this.showJsonEditor = true
          this.validateJsonString()
        }
      } catch (e) {
        // 不是JSON格式，保持文本模式
        this.showJsonEditor = false
      }
    },

    // 切换编辑器模式
    toggleEditor() {
      this.showJsonEditor = !this.showJsonEditor
      if (this.showJsonEditor) {
        this.jsonString = this.form.value || ''
        this.validateJsonString()
      } else {
        this.jsonError = ''
        this.jsonValid = false
      }
    },

    // JSON输入处理
    onJsonInput() {
      this.form.value = this.jsonString
      this.validateJsonString()
    },

    // 验证JSON格式
    validateJsonString() {
      if (!this.jsonString || !this.jsonString.trim()) {
        this.jsonError = ''
        this.jsonValid = false
        return
      }

      try {
        JSON.parse(this.jsonString)
        this.jsonError = ''
        this.jsonValid = true
      } catch (e) {
        this.jsonError = `JSON格式错误: ${e.message}`
        this.jsonValid = false
      }
    },

    // 格式化JSON
    formatJson() {
      if (!this.jsonString || !this.jsonString.trim()) {
        this.$xMsgWarning('请先输入JSON内容')
        return
      }

      try {
        const parsed = JSON.parse(this.jsonString)
        this.jsonString = JSON.stringify(parsed, null, 2)
        this.form.value = this.jsonString
        this.jsonError = ''
        this.jsonValid = true
        this.$xMsgSuccess('JSON格式化成功')
      } catch (e) {
        this.$xMsgError(`JSON格式化失败: ${e.message}`)
      }
    },

    // 验证JSON
    validateJson() {
      this.validateJsonString()
      if (this.jsonValid) {
        this.$xMsgSuccess('JSON格式验证通过')
      } else if (this.jsonError) {
        this.$xMsgError(this.jsonError)
      } else {
        this.$xMsgWarning('请输入JSON内容')
      }
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          if (this.isEdit) {
            const res = await actionConfigApi.editActionConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await actionConfigApi.addActionConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style scoped>
/* JSON编辑器样式 */
.json-editor {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.json-editor ::v-deep .el-textarea__inner {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.text-editor ::v-deep .el-textarea__inner {
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
}

/* JSON错误提示 */
.json-error {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.json-error i {
  margin-right: 6px;
  font-size: 14px;
}

/* JSON成功提示 */
.json-success {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #409eff;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.json-success i {
  margin-right: 6px;
  font-size: 14px;
}

/* 按钮样式优化 */
.el-button--small {
  padding: 7px 15px;
  font-size: 12px;
  border-radius: 4px;
}

/* 卡片样式 */
.el-card {
  border: 1px solid #ebeef5;
}

/* 编辑器头部样式 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .editor-header > div {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .el-button--small {
    padding: 6px 12px;
    font-size: 11px;
  }
}
</style>
