﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>
<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","VOs");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}
#>

<# foreach (var item in DbHelper.GetDbTablesNew(config.ConnectionString, config.DbDatabase,config.TableName))
   {
		var tableName=item.ToString();
		manager.StartBlock(tableName+"VO.cs",outputPath);//文件名
#>

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace <#=ProjectName#>.Entity.VOs
{
	 ///<summary>
	 ///<#=tableName#>VO
	 ///  此代码由T4模板自动生成
	 ///  生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	 ///</summary>
	 public partial class <#=tableName#>VO
	 {
	 <# foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName)){#>

		/// <summary>
        /// <#= column.Remark == "" ? column.ColumnName : column.Remark.Replace("\r\n"," ") #>
        /// </summary>
		public <#= column.CSharpType#><# if(column.CommonType.IsValueType && column.IsNullable){#>?<#}#> <#=column.ColumnName#> { get; set; }
	 <#
	 }
	 #> 
	 }
}

<#
   manager.EndBlock(); 
   }
   manager.Process(true);
#>