﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Data;

namespace TestForm.Extensions
{
    public static class DatabaseServiceCollectionExtensions
    {
        public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
        {
            var connectionString = configuration.GetConnectionString("ProjAuthConnstr");
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Connection string 'ProjAuthConnstr' not found.");
            }

            // 注册 IDbConnection，每次请求一个新的 SqlConnection 实例
            services.AddScoped<IDbConnection>(sp => new SqlConnection(connectionString));

            return services;
        }
    }
}
