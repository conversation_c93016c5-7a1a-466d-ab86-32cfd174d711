﻿using Amazon.S3;
using Amazon.S3.Model;
using ECloud.Configs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ECloud.Apis.EOS
{
    public class BucketApi : EOSBaseApi
    {
        public BucketApi(string accessKeyId, string accessKeySecret)
            : base(accessKeyId, accessKeySecret, DataCenterBaseEndpointConfig.BaseEndpoint)
        {
        }

        public BucketApi(string accesskey, string secretkey, string endpoint)
            : base(accesskey, secretkey, endpoint)
        {
        }

        public ListBucketsResponse ListBuckets()
        {
            var buckets = EOSClient
                .ListBucketsAsync()
                .GetAwaiter()
                .GetResult();

            if (buckets.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                return buckets;
            }
            else
            {
                throw new Exception($"Failed to list buckets: {buckets.HttpStatusCode}");
            }
        }

        public string GetBucketLocation(string bucketName)
        {
            var getBucketLocationRequest = new GetBucketLocationRequest()
            {
                BucketName = bucketName
            };

            var response = EOSClient
                .GetBucketLocationAsync(getBucketLocationRequest)
                .GetAwaiter()
                .GetResult();

            return response?.Location?.ToString() ?? string.Empty;
        }

        public PutBucketResponse CreateBucket(string bucketName)
        {
            var putBucketRequest = new PutBucketRequest()
            {
                BucketName = bucketName,
                UseClientRegion = true,
                CannedACL = S3CannedACL.PublicRead,
            };
            var response = EOSClient
                .PutBucketAsync(putBucketRequest)
                .GetAwaiter()
                .GetResult();
            if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                return response;
            }
            else
            {
                throw new Exception($"Failed to create bucket: {response.HttpStatusCode}");
            }
        }

        public bool DeleteBucket(string bucketName)
        {
            var deleteBucketRequest = new DeleteBucketRequest()
            {
                BucketName = bucketName
            };
            var response = EOSClient
                .DeleteBucketAsync(deleteBucketRequest)
                .GetAwaiter()
                .GetResult();

            return response.HttpStatusCode == System.Net.HttpStatusCode.OK || response.HttpStatusCode == System.Net.HttpStatusCode.NoContent;
        }

        public bool SetBucketWebsite(string bucketName, string indexDocumentSuffix, string errorDocument)
        {
            PutBucketWebsiteRequest putRequest = new PutBucketWebsiteRequest()
            {
                BucketName = bucketName,
                WebsiteConfiguration = new WebsiteConfiguration()
                {
                    IndexDocumentSuffix = indexDocumentSuffix,
                    ErrorDocument = errorDocument
                }
            };
            var response = EOSClient
                .PutBucketWebsiteAsync(putRequest)
                .GetAwaiter()
                .GetResult();

            return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }

        public bool HeadBucket(string bucketName)
        {
            var headBucketRequest = new HeadBucketRequest()
            {
                BucketName = bucketName
            };

            var response = EOSClient
                .HeadBucketAsync(headBucketRequest)
                .GetAwaiter()
                .GetResult();

            return response != null && response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }
    }
}
