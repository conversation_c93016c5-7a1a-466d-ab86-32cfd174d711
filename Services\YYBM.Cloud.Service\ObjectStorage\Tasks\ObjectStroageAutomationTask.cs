﻿using YYBM.Cloud.Service.ObjectStorage.Clients;
using ZProjectBase.Common.ITask;

namespace YYBM.Cloud.Service.ObjectStorage.Tasks
{
    public class ObjectStroageAutomationTask : ITaskAsync
    {
        protected readonly AliyunObjectStroageClient _aliyunObjectStroageClient;
        protected readonly ECloudObjectStroageClient _eCloudObjectStroageClient;

        public ObjectStroageAutomationTask(AliyunObjectStroageClient aliyunObjectStroageClient, ECloudObjectStroageClient eCloudObjectStroageClient)
        {
            _aliyunObjectStroageClient = aliyunObjectStroageClient;
            _eCloudObjectStroageClient = eCloudObjectStroageClient;
        }

        public async Task StartAsync()
        {
            await _aliyunObjectStroageClient.Run();
            await _eCloudObjectStroageClient.Run();
        }
    }
}
