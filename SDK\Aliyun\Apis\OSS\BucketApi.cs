﻿using Aliyun.OSS;

namespace Aliyun.Apis.OSS
{
    /// <summary>
    /// 存储空间API
    /// </summary>
    public class BucketApi : OSSBaseApi
    {
        public BucketApi(string accessKeyId, string accessKeySecret, string region, string endpoint)
            : base(accessKeyId, accessKeySecret, region, endpoint)
        {
        }

        /// <summary>
        /// 查询所有存储空间
        /// </summary>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public ListBucketsResult ListBuckets()
        {
            var listBucketsRequest = new ListBucketsRequest()
            {
                MaxKeys = 1000,
            };

            var buckets = OSSClient.ListBuckets(listBucketsRequest);
            if (buckets == null || !buckets.Buckets.Any())
            {
                throw new Exception("No buckets found.");
            }

            //foreach (var bucket in buckets.Buckets)
            //{
            //    Console.WriteLine($"Bucket Name: {bucket.Name}, Creation Date: {bucket.CreationDate}");
            //}

            return buckets;
        }

        /// <summary>
        /// 创建存储空间
        /// </summary>
        /// <param name="bucketName"></param>
        /// <returns></returns>
        public Bucket CreateBucket(string bucketName)
        {
            var putBucketRequest = new CreateBucketRequest(bucketName)
            {
                ACL = CannedAccessControlList.PublicRead, // 设置为公共读
            };
            return OSSClient.CreateBucket(putBucketRequest);
        }

        /// <summary>
        /// 删除存储空间
        /// </summary>
        /// <param name="bucketName"></param>
        /// <returns></returns>
        public bool DeleteBucket(string bucketName)
        {
            try
            {
                if (DoesBucketExist(bucketName) == false)
                    throw new Exception("Bucket does not exist.");

                OSSClient.DeleteBucket(bucketName);

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 判断存储空间是否存在
        /// </summary>
        /// <param name="bucketName"></param>
        /// <returns></returns>
        public bool DoesBucketExist(string bucketName)
        {
            return OSSClient.DoesBucketExist(bucketName);
        }

        /// <summary>
        /// 设置存储空间的静态网站托管
        /// </summary>
        /// <param name="bucektName"></param>
        /// <param name="indexDocument"></param>
        /// <param name="errorDocument"></param>
        /// <returns></returns>
        public bool SetBucketWebsite(string bucektName, string indexDocument, string errorDocument)
        {
            try
            {
                var setBucketWebsiteRequest = new SetBucketWebsiteRequest(bucektName, indexDocument, errorDocument);

                OSSClient.SetBucketWebsite(setBucketWebsiteRequest);

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
