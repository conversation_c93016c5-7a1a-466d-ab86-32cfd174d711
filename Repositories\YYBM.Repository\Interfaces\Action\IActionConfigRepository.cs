﻿using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.IRepository
{
    /// <summary>
    /// IActionConfigRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-07-18 15:31:38 
    /// </summary>	
    public interface IActionConfigRepository : IBaseRepository<ActionConfigModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<ActionConfigModel> SelectPage(ActionConfigParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 查询
        /// </summary>
        ActionConfigModel SelectActionConfig(string ActionFlag);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertActionConfig(ActionConfigModel actionConfig);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateActionConfig(ActionConfigModel actionConfig);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteActionConfig(string ActionFlag);
    }
}
