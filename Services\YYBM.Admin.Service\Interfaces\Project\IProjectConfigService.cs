//----------ProjectConfig开始----------

using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using YYBM.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// ProjectConfigService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 18:12:15 
    /// </summary>	
    public interface IProjectConfigService : IBaseService<ProjectConfigModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<ProjectConfigVO> GetTableList(ProjectConfigParams searchParams);

        /// <summary>
        /// 详情
        /// </summary>
        ProjectConfigVO GetDetail(int Id);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddProjectConfig(ProjectConfigParams projectConfigParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateProjectConfig(ProjectConfigParams projectConfigParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteProjectConfig(ProjectConfigParams projectConfigParams);

    }
}

//----------ProjectConfig结束----------
