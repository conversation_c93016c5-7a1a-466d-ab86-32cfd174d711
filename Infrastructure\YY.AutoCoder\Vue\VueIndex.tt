﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","VueIndex");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}

    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
    foreach (var item in DbHelper.GetDbTablesNew(config.ConnectionString, config.DbDatabase,config.TableName))
    {
        var tableName=item.ToString();
        var camelCaseName = tableName[0].ToString().ToLower() + tableName.Substring(1);
        manager.StartBlock(tableName+"VueIndex"+".vue",outputPath);
#>


<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" label-width="80px" inline size="mini">
<# foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName)){
        var camelName = column.ColumnName.ToString()[0].ToString().ToLower() + column.ColumnName.ToString().Substring(1);
        var columnComment = column.Remark == "" ? column.ColumnName : column.Remark.Replace("\r\n"," ");
#>
		<el-form-item label="<#= columnComment #>">
          <el-input v-model="queryParams.<#= camelName #>"></el-input>
        </el-form-item>
<#
}
#> 
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search"  @click="$refs.table.refresh(true)">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh"  @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAdd" v-permission="['<#=camelCaseName#>:add']">新增</el-button>
      </el-row>
      
      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
<# foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName)){
        var camelName = column.ColumnName.ToString()[0].ToString().ToLower() + column.ColumnName.ToString().Substring(1);
        var columnComment = column.Remark == "" ? column.ColumnName : column.Remark.Replace("\r\n"," ");
#>
        <el-table-column prop="<#= camelName #>" label="<#= camelName #>" align="center" min-width="100"></el-table-column>
<#
}
#> 
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button size="mini" 
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['<#=camelCaseName#>:edit']"
            >修改</el-button>
            <el-button size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['<#=camelCaseName#>:delete']"
            >删除</el-button>
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk"/>
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { <#=camelCaseName#>Api } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    EditDialog
  },
  mixins:[tableHeightMixin],
  data () {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset () {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await <#=camelCaseName#>Api.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd () {
      this.$refs.editDialog.add()
    },
    handleEdit (row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete (row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning'
      }).then(async () => {
        this.$xloading.show()
        const res = await <#=camelCaseName#>Api.del<#=tableName#>(row.id)
        this.$xloading.hide()
        if (res.code == 0) {
          this.$refs.table.refresh()
          this.$xMsgSuccess('删除成功')
        } else {
          this.$xMsgError('删除失败！' + res.msg)
        }
      }).catch(() => {
        this.$xloading.hide()
      })
      
    },

    handleOk () {
      this.$refs.table.refresh()
    },
  }
}
</script>
<#
   manager.EndBlock(); 
   }
   manager.Process(true);
#>