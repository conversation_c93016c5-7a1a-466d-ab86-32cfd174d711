//----------Account开始----------

using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.IRepository
{
    /// <summary>
    /// IAccountRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:24 
    /// </summary>	
    public interface IAccountRepository : IBaseRepository<AccountModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<AccountModel> SelectPage(AccountParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertAccount(AccountModel account);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateAccount(AccountModel account);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteAccount(int Id);
    }
}

//----------Account结束----------  
