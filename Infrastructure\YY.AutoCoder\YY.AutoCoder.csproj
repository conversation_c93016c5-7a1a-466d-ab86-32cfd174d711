<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9F686754-61FA-470F-92C3-8FC2E444E737}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YY.AutoCoder</RootNamespace>
    <AssemblyName>YY.AutoCoder</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Model\Model.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Model\Params.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Model\VO.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Repository\EFContext.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Repository\EFRepository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Repository\IEFRepository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Repository\IRepository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Repository\Repository.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Service\IService.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Service\Service.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Vue\VueApi.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Vue\VueEdit.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
    <Content Include="Vue\VueIndex.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="DbHelper.ttinclude" />
    <None Include="Global.ttinclude" />
    <None Include="Model.ttinclude" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>