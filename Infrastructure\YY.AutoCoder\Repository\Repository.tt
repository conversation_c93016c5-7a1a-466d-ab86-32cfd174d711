﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","Repositories");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}
#>

//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//	   生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# 
	var tableName=config.TableName;
 #>
<# 
if(tableName!=""){
#>  

using ZProjectBase.Mvc;
using YY.Infrastructure.SqlDb;
using <#=ProjectName#>.IRepository;
using <#=ProjectName#>.Entity.Models;


namespace <#=ProjectName#>.Repository
{	
	/// <summary>
	/// <#=tableName#>Repository
	///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	/// </summary>	
	public partial class <#=tableName#>Repository : DefConnRepository<<#=tableName#>>
    {
		
    }
}

<# 
} else{ 
#>

<# 
    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
#>

<# 
foreach(System.Data.DataRow row in schema.Rows) 
{  
	manager.StartBlock(row["TABLE_NAME"].ToString()+"Repository"+".cs",outputPath);
    var primaryKey = "";
    var cSharpType = "";   
    var deleteMethodParams = "";
    foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, row["TABLE_NAME"].ToString()))
    {
         if(column.IsPrimaryKey) 
         {
            cSharpType = column.CSharpType;
            primaryKey = column.ColumnName;
            deleteMethodParams = "("+ cSharpType +" "+ primaryKey +")";
         } 
    }
	var tableNameModel = row["TABLE_NAME"].ToString()+ "Model";
    var paramsName = row["TABLE_NAME"].ToString() + "Params";
    var camelCaseName = row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1);
    var interfaceName = "I" + row["TABLE_NAME"].ToString() + "Repository";
    var selectMethod = "Select" + row["TABLE_NAME"].ToString() + deleteMethodParams;
    var insertMethod = "Insert" + row["TABLE_NAME"].ToString() + "(";
    var updateMethod = "Update" + row["TABLE_NAME"].ToString() + "(";
    var deleteMethod = "Delete" + row["TABLE_NAME"].ToString() + deleteMethodParams;
    var idName = camelCaseName + ".Id";
	#>
//----------<#=row["TABLE_NAME"].ToString()#>开始----------   

using ZProjectBase.Mvc;
using YY.Infrastructure.SqlDb;
using <#=ProjectName#>.IRepository;
using <#=ProjectName#>.Entity.Models;

namespace <#=ProjectName#>.Repository
{	
	/// <summary>
	/// <#=row["TABLE_NAME"].ToString()#>Repository
	///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	/// </summary>	
	public class <#=row["TABLE_NAME"].ToString()#>Repository : AdminConnRespository<<#=tableNameModel#>>,I<#=row["TABLE_NAME"].ToString()#>Repository
    {
        public <#=row["TABLE_NAME"].ToString()#>Repository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<<#=tableNameModel#>> SelectPage(<#=paramsName#> searchParams, PageInfo pageInfo)
        {
            var pageResult = new PageResult<<#=tableNameModel#>>();
            long total;
            pageResult.Data = conn.GetByPage<<#=tableNameModel#>>(pageInfo.page, pageInfo.limit, out total, param: searchParams);
            pageResult.Count = total;
            return pageResult;
        }
        <# if(cSharpType != "int"){ #>

        /// <summary>
        /// 查询
        /// </summary>
        public <#=tableNameModel#> <#=selectMethod#>
        {
            string whereSql = "where <#=primaryKey#> = @<#=primaryKey#>";
            return conn.GetByWhereFirst<<#=tableNameModel#>>(whereSql,new {<#=primaryKey#> = <#=primaryKey#>});
        }
        <#}#>

        /// <summary>
        /// 新增
        /// </summary>
        public bool <#=insertMethod#><#=tableNameModel#> <#=camelCaseName#>)
        {
            var res = conn.Insert(<#=camelCaseName#>) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool <#=updateMethod#><#=tableNameModel#> <#=camelCaseName#>)
        {
            return conn.UpdateById(<#=camelCaseName#>) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool <#=deleteMethod#>
        {
<# 
            if(primaryKey == "")
            {
#>
            throw new NotImplementedException();
<#
            }
            else {
            #>
<# if(cSharpType == "int"){ #>
            return conn.DeleteById<<#=tableNameModel#>>(<#=primaryKey#>) > 0;
<#}
            else{ 
#>
            string whereSql = "where <#=primaryKey#> = @<#=primaryKey#>";
            return conn.DeleteByWhere<<#=tableNameModel#>>(whereSql,new {<#=primaryKey#> = <#=primaryKey#>}) > 0;
<#}#><#}#>
        }
    }
}

//----------<#=row["TABLE_NAME"].ToString()#>结束----------
    
<# 
	manager.EndBlock(); 
	} 
	manager.Process(true);
}
#> 
