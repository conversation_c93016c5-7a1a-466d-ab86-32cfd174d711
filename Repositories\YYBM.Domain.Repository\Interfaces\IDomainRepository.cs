//----------Domain开始----------

using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Domain.IRepository
{
    /// <summary>
    /// IDomainRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:52 
    /// </summary>	
    public interface IDomainRepository : IBaseRepository<DomainModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<DomainModel> SelectPage(DomainParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertDomain(DomainModel domain);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateDomain(DomainModel domain);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteDomain(int Id);
    }
}

//----------Domain结束----------  
