//----------Httpspackage开始----------

using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.IRepository
{
    /// <summary>
    /// IHttpspackageRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:24 
    /// </summary>	
    public interface IHttpspackageRepository : IBaseRepository<HttpspackageModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<HttpspackageModel> SelectPage(HttpspackageParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertHttpspackage(HttpspackageModel httpspackage);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateHttpspackage(HttpspackageModel httpspackage);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteHttpspackage(int Id);

        Task<HttpspackageModel> SelectAsyncByAccount(int accountId);
    }
}

//----------Httpspackage结束----------  
