//----------OSSBucket开始----------

using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// OSSBucketService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 19:15:42 
    /// </summary>	
    public interface IOSSBucketService : IBaseService<OSSBucketModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<OSSBucketVO> GetTableList(OSSBucketParams searchParams);

        /// <summary>
        /// 查询
        /// </summary>
        OSSBucketVO GetDetail(string Bucket);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddOSSBucket(OSSBucketParams oSSBucketParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateOSSBucket(OSSBucketParams oSSBucketParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteOSSBucket(OSSBucketParams oSSBucketParams);

    }
}

//----------OSSBucket结束----------
