﻿using YYBM.Cloud.Entity.Models;
using ZProjectBase.Common.Extend;
using ZProjectBase.Common.Utils;

namespace YYBM.Cloud.Service.ObjectStorage.Mappers
{
    public class RegionEndpointsConfigMapper
    {
        public static List<RegionEndpointsConfig> MaptoRegionEndpointsConfigList(OSSConfigModel oSSConfig)
        {
            var regionEndpointsConfig = oSSConfig.RegionEndpointsConfig;
            if (regionEndpointsConfig.IsNullOrEmpty())
            {
                throw new ArgumentException("RegionEndpointsConfig cannot be null or empty.");
            }

            var regionEndpointsConfigs = new List<RegionEndpointsConfig>();

            var regionEndpoints = regionEndpointsConfig.Splitline();
            foreach (var item in regionEndpoints)
            {
                if (item.StartsWith("--"))
                    continue;

                var arrary = item.Split('@');
                var region = arrary[0].Trim();
                var endpoint = arrary.Length >= 1 ? arrary[1].Trim() : string.Empty;
                var weight = arrary.Length >= 2 ? arrary[2].Trim().ToInt() : 0;

                regionEndpointsConfigs.Add(new RegionEndpointsConfig()
                {
                    Region = region,
                    Endpoint = endpoint,
                    Weight = weight
                });
            }

            return regionEndpointsConfigs;
        }

        public static RegionEndpointsConfig GetRandomRegionEndpointConfig(OSSConfigModel oSSConfig)
        {
            return RandomUtils.Random<RegionEndpointsConfig>(MaptoRegionEndpointsConfigList(oSSConfig));
        }
    }

    public class RegionEndpointsConfig : IWeightedItem
    {
        public string Region { get; set; }
        public string Endpoint { get; set; }
        public int Weight { get; set; }
    }
}
