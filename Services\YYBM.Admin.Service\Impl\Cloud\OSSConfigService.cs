//----------OSSConfig开始----------

using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;
using YYBM.Domain.Entity.VOs;
using YYBM.Domain.IRepository;
using YYBM.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    /// <summary>
    /// OSSConfigService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 16:15:16 
    /// </summary>	
    public class OSSConfigService : AdminServiceBase<OSSConfigModel>, IOSSConfigService
    {

        private readonly IOSSConfigRepository _oSSConfigRepository;
        private readonly IProjectConfigRepository _projectConfigRepository;
        private readonly IDomainOSSConfigRepository _domainOSSConfigRepository;
        private readonly IDomainGroupRepository _domainGroupRepository;

        public OSSConfigService(IHttpContextAccessor httpContextAccessor, IOSSConfigRepository oSSConfigRepository, IDomainOSSConfigRepository domainOSSConfigRepository, IDomainGroupRepository domainGroupRepository, IProjectConfigRepository projectConfigRepository) : base(httpContextAccessor)
        {
            _oSSConfigRepository = oSSConfigRepository;
            _domainOSSConfigRepository = domainOSSConfigRepository;
            _domainGroupRepository = domainGroupRepository;
            _projectConfigRepository = projectConfigRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<OSSConfigVO> GetTableList(OSSConfigParams searchParams)
        {
            var pageResult = _oSSConfigRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            var tableResult = CreateTableResult<OSSConfigModel, OSSConfigVO>(pageResult);
            foreach (var item in tableResult.Data)
            {
                item.ProjName = _projectConfigRepository.Select(item.ProjId)?.Name ?? "未找到";

                var domainOSSConfig = _domainOSSConfigRepository.Select(item.DomainOssConfigId);
                var domainOssConfigVO = MapperIns.Map<DomainOSSConfigVO>(domainOSSConfig);
                domainOssConfigVO.DomainGroupName = _domainGroupRepository.Select(domainOSSConfig.DomainGroupId.Value)?.Name ?? "未分组";
                item.DomainOssConfig = domainOssConfigVO;
            }
            return tableResult;
        }


        /// <summary>
        /// 查询
        /// </summary>
        public OSSConfigVO GetDetail(string Id)
        {
            var oSSConfig = _oSSConfigRepository.SelectOSSConfig(Id);
            if (oSSConfig == null)
            {
                throw new BusinessException("数据不存在");
            }
            var oSSConfigVO = MapperIns.Map<OSSConfigVO>(oSSConfig);
            return oSSConfigVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddOSSConfig(OSSConfigParams oSSConfigParams)
        {
            var oSSConfig = MapperIns.Map<OSSConfigModel>(oSSConfigParams);
            oSSConfig.CreateOn = DateTime.Now;
            var result = _oSSConfigRepository.InsertOSSConfig(oSSConfig);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateOSSConfig(OSSConfigParams oSSConfigParams)
        {
            var oSSConfig = MapperIns.Map<OSSConfigModel>(oSSConfigParams);
            oSSConfig.EditTime = DateTime.Now;
            var result = _oSSConfigRepository.UpdateOSSConfig(oSSConfig);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteOSSConfig(OSSConfigParams oSSConfigParams)
        {
            CheckParamsUtils.Failure(string.IsNullOrEmpty(oSSConfigParams.Id), "Id不能为空");

            var result = _oSSConfigRepository.DeleteOSSConfig(oSSConfigParams.Id);
            return ResponseResult.Result(result);
        }

    }
}

//----------OSSConfig结束----------
