using System.ComponentModel.DataAnnotations;
using YYBM.Cloud.Entity.Enums;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Cloud.Entity.Models
{
    ///<summary>
    ///OSSConfig
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-18 16:15:09 
    ///</summary>
    [Table("OSSConfig")]
    public partial class OSSConfigModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(false)]
        [Required]
        public string Id { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [Required]
        public int ProjId { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        [Required]
        public CloudPlatform Platform { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 文件路径，多个使用换行分隔
        /// </summary>
        [Required]
        public string FilesPath { get; set; }

        /// <summary>
        /// 静态网站文件路径
        /// </summary>
        public string WebsiteFilePath { get; set; }

        /// <summary>
        /// 区域与服务器节点设置
        /// </summary>
        [Required]
        public string RegionEndpointsConfig { get; set; }

        /// <summary>
        /// 账号、权重设置
        /// </summary>
        [Required]
        public string AccountsWeightConfig { get; set; }

        /// <summary>
        /// 域名保持设置Id
        /// </summary>
        [Required]
        public int DomainOssConfigId { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        [Required]
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Required]
        public bool Enable { get; set; }
    }
}

