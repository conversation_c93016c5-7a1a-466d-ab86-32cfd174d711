﻿using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.Service.ObjectStorage.DTOs;

namespace YYBM.Cloud.Service.ObjectStorage.Interfaces
{
    public interface IObjectStorageBucket
    {
        IEnumerable<Bucket> GetBuckets(AccountVO accountVO);

        bool CreateBucket(string bucketName, AccountVO accountVO);

        bool DeleteBucket(string bucketName, AccountVO accountVO);

        bool DoesBucketExist(string bucketName, AccountVO accountVO);

        bool SetBucketWebsite(string bucektName, string indexDocument, string errorDocument, AccountVO accountVO);
    }
}
