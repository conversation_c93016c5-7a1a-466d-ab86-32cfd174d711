<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-YYBM.WS.Cloud-63e61bf1-1da0-4336-8e49-316eccd529f5</UserSecretsId>
	<SatelliteResourceLanguages>en;zh-Hans</SatelliteResourceLanguages>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="8.0.1" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.15.0" />
    <PackageReference Include="Scrutor" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Services\YYBM.Cloud.Service\YYBM.Cloud.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Configs\log4net_config.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
