//----------ProjectConfig开始----------   

using Microsoft.EntityFrameworkCore;
using System.Text;
using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using YYBM.IRepository;
using ZProjectBase.DB.EF.Repository;
using ZProjectBase.DB.Extend;
using ZProjectBase.Mvc;

namespace YYBM.Repository
{
    /// <summary>
    /// ProjectConfigRepository (EF)
    /// </summary>
    public class ProjectConfigRepository : EFBaseRepository<ProjectConfigModel>, IProjectConfigRepository
    {
        public ProjectConfigRepository(DbContextOptions<ProjectConfigRepository> options) : base(options) { }

        public DbSet<ProjectConfigModel> ProjectConfigs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Entity<ProjectConfigModel>(entity =>
            {
                entity.ToTable("ProjectConfig");

                entity.HasKey(e => e.Id);
            });
        }

        public PageResult<ProjectConfigModel> SelectPage(ProjectConfigParams searchParams, PageInfo pageInfo)
        {
            var q = ProjectConfigs.AsQueryable();
            if (searchParams.Id > 0)
                q = q.Where(x => x.Id == searchParams.Id);
            if (searchParams.ParentId > 0)
                q = q.Where(x => x.ParentId == searchParams.ParentId);
            if (searchParams.Symbol.IsNotNullOrEmpty())
                q = q.Where(x => x.Symbol.Contains(searchParams.Symbol));
            if (searchParams.Name.IsNotNullOrEmpty())
                q = q.Where(x => x.Name.Contains(searchParams.Name));

            q = q.OrderBy(x => x.Id);

            var total = q.LongCount();
            var skip = pageInfo.page > 1 ? (pageInfo.page - 1) * pageInfo.limit : 0;
            var data = q.Skip(skip).Take(pageInfo.limit).ToList();
            return new PageResult<ProjectConfigModel> { Data = data, Count = total };
        }

        public ProjectConfigModel SelectProjectConfig(int id)
        {
            return ProjectConfigs.FirstOrDefault(x => x.Id == id);
        }

        public bool InsertProjectConfig(ProjectConfigModel projectConfig)
        {
            ProjectConfigs.Add(projectConfig);
            return SaveChanges() > 0;
        }

        public bool UpdateProjectConfig(ProjectConfigModel projectConfig)
        {
            ProjectConfigs.Update(projectConfig);
            return SaveChanges() > 0;
        }

        public bool DeleteProjectConfig(int id)
        {
            var entity = SelectProjectConfig(id);
            if (entity == null) return false;
            ProjectConfigs.Remove(entity);
            return SaveChanges() > 0;
        }
    }
}

//----------ProjectConfig结束----------

