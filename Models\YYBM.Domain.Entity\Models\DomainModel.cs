using System.ComponentModel.DataAnnotations;
using YYBM.Domain.Entity.Enums;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Domain.Entity.Models
{
    ///<summary>
    ///Domain
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-15 19:38:49 
    ///</summary>
    [Table("Domain")]
    public partial class DomainModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(true)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [Required]
        public int ProjId { get; set; }

        /// <summary>
        /// 分组Id
        /// </summary>
        [Required]
        public int GroupId { get; set; }

        [Computed]
        public string? GroupName { get; set; }

        /// <summary>
        /// Domain
        /// </summary>
        [Required]
        public string Domain { get; set; }

        /// <summary>
        /// 权重
        /// </summary>
        [Required]
        public int Weight { get; set; }

        /// <summary>
        /// 1：上线，0：下线，-1：禁用
        /// </summary>
        [Required]
        public DomainState State { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        [Required]
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

        /// <summary>
        /// 是否随机域名
        /// </summary>
        public bool? IsRandomSLD { get; set; }

        /// <summary>
        /// 是否OSS域名
        /// </summary>
        [Required]
        public bool IsOSS { get; set; }
    }
}

