//----------Httpspackage开始----------   

using Microsoft.Extensions.Configuration;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.IRepository;
using YYBM.Repository.SqlDb;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.Repository
{
    /// <summary>
    /// HttpspackageRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:25 
    /// </summary>	
    public class HttpspackageRepository : YYBMCloudRepository<HttpspackageModel>, IHttpspackageRepository
    {
        public HttpspackageRepository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<HttpspackageModel> SelectPage(HttpspackageParams searchParams, PageInfo pageInfo)
        {
            var pageResult = new PageResult<HttpspackageModel>();
            long total;
            pageResult.Data = conn.GetByPage<HttpspackageModel>(pageInfo.page, pageInfo.limit, out total, param: searchParams);
            pageResult.Count = total;
            return pageResult;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public bool InsertHttpspackage(HttpspackageModel httpspackage)
        {
            var res = conn.Insert(httpspackage) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool UpdateHttpspackage(HttpspackageModel httpspackage)
        {
            return conn.UpdateById(httpspackage) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool DeleteHttpspackage(int Id)
        {
            return conn.DeleteById<HttpspackageModel>(Id) > 0;
        }

        public async Task<HttpspackageModel> SelectAsyncByAccount(int accountId)
        {
            var whereSql = "where AccountId = @AccountId";
            return await conn.GetByWhereFirstAsync<HttpspackageModel>(whereSql, new { AccountId = accountId });
        }
    }
}

//----------Httpspackage结束----------

