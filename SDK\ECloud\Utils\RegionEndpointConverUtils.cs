﻿using ECloud.Apis.EOS;
using ECloud.Configs;

namespace ECloud.Utils
{
    public class RegionEndpointConverUtils
    {
        public static string GetRegion(string ak, string sk, string bucketName)
        {
            var bucketApi = new BucketApi(ak, sk);
            var region = bucketApi.GetBucketLocation(bucketName);
            return region;
        }
        public static string RegionConverToEndpoint(string region)
        {
            if (region == null) return "";
            return RegionCollectConfig.RegionEndpointDict.FirstOrDefault(o => o.Key == region).Value;
        }

        public static string EndpointConverToRegion(string endpoint)
        {
            if (string.IsNullOrEmpty(endpoint)) return "";
            return RegionCollectConfig.RegionEndpointDict.FirstOrDefault(o => o.Value == endpoint).Key;
        }

        public static string EndpointConverToHost(string endpoint)
        {
            if (string.IsNullOrEmpty(endpoint)) return null;
            var uri = new Uri(endpoint);
            return uri.Host;
        }
    }
}
