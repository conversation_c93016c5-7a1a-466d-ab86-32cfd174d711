﻿using Microsoft.Extensions.Configuration;
using ZProjectBase.DB.SqlServer.DB.SQLExts;
using ZProjectBase.DB.SqlServer.Repository;

namespace YYBM.Domain.Repository.SqlDb
{
    public class YYBMDomainRepository<T> : SqlServerBaseRepository<T> where T : class, new()
    {
        public YYBMDomainRepository(IConfiguration configuration)
        {
            string? connstr = configuration.GetConnectionString("YYBMDomainConnstr");
            conn = new SqlServerExt(connstr);
        }
    }
}
