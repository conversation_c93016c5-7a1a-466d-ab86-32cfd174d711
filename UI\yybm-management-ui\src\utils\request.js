import axios from 'axios'
import { MessageBox, Message, Notification, Loading } from 'element-ui'
import store from '@/store'
import router from '@/router'
import { getToken } from '@/utils/auth'
import GlobalLoading from '@/components/GlobalLoading'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 30000, // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    // do something before request is sent
    const token = getToken()
    if (token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Access-Token'] = token
    }
    const projectInfo = {}
    if (config.method === 'get') {
      config.params = Object.assign(config.params || {}, projectInfo)
    }
    if (config.method === 'post') {
      config.data = Object.assign(config.data || {}, projectInfo)
    }

    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    const res = response.data
    // console.log(res)
    // if the custom code is not 20000, it is judged as an error.
    if (res.code !== 0) {
      // Message({
      //   message: res.msg || 'Error',
      //   type: 'error',
      //   duration: 3 * 1000
      // })
      // Notification.error({
      //   title: res.msg || 'Error'
      // })

      // return Promise.reject(new Error())
      return res
    } else {
      return res
    }
  },
  (error) => {
    console.log('err: ', error)
    console.log('err: ', error.message)
    console.log('err: ', error.response)
    // TODO: 关闭loading
    GlobalLoading.hide()
    if (error.response.status == 401) {
      store.dispatch('stopPolling')
      if (error.response.request.responseURL.indexOf('/api/logout') != -1) {
        return
      }
      // 重新登录
      MessageBox.confirm('登录状态已过期，请重新登录', '系统提示', {
        confirmButtonText: '重新登录',
        type: 'warning',
        showCancelButton: false,
      }).then(() => {
        store.dispatch('Logout').then(() => {
          router.replace({ path: '/login' })
        })
      })
      return Promise.reject(error)
    } else if (error.response.status == 403) {
      Message({
        message: error.response.data.msg,
        type: 'error',
        duration: 5 * 1000,
      })
      return Promise.reject(error)
    }
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000,
    })
    return Promise.reject(error)
  }
)

export default service
