import * as loginApi from '@/api/system/login'
import * as menu<PERSON>pi from '@/api/system/menu'
import * as roleApi from '@/api/system/role'
import * as sysUserApi from '@/api/system/user'
import * as authApi from '@/api/system/auth'
import * as redisApi from '@/api/system/redis'
import * as systemNoticeApi from '@/api/system/notice'

import * as fileApi from '@/api/file'
import * as optionsApi from '@/api/options'

import * as actionConfigApi from '@/api/action/actionconfig'

import * as accountApi from '@/api/cloud/account'
import * as ossConfigApi from '@/api/cloud/ossconfig'
import * as ossBucketApi from '@/api/cloud/ossbucket'

import * as dashboardApi from '@/api/stats/dashboard'

import * as projectConfigApi from '@/api/project/projectconfig'

import * as domainApi from '@/api/domain/domain'
import * as domainGroupApi from '@/api/domain/domaingroup'
import * as domainOSSConfigApi from '@/api/domain/domainossconfig'

export {
  loginApi,
  menuApi,
  roleApi,
  sysUserApi,
  authApi,
  redisApi,
  systemNoticeApi,
  fileApi,
  optionsApi,
  actionConfigApi,
  accountApi,
  ossConfigApi,
  ossBucketApi,
  dashboardApi,
  projectConfigApi,
  domainApi,
  domainGroupApi,
  domainOSSConfigApi,
}
