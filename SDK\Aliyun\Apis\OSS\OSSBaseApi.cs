﻿
using Aliyun.OSS;
using <PERSON><PERSON>.OSS.Common;

namespace Aliyun.Apis.OSS
{
    public class OSSBaseApi : BaseApi
    {
        protected OssClient OSSClient;

        public OSSBaseApi(string accessKeyId, string accessKeySecret, string region, string endpoint)
        {
            // 创建ClientConfiguration实例，按照您的需要修改默认参数。
            var conf = new ClientConfiguration();
            // 设置v4签名。
            conf.SignatureVersion = SignatureVersion.V4;

            // 创建OssClient实例。
            OSSClient = new OssClient(endpoint, accessKeyId, accessKeySecret, conf);
            OSSClient.SetRegion(region);
        }
    }
}
