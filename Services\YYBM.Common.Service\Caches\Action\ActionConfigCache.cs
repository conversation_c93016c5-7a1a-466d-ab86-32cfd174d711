﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YYBM.Entity.Action;
using YYBM.Entity.Models;
using YYBM.IRepository;
using ZProjectBase.Common;
using ZProjectBase.Common.Cache;
using ZProjectBase.Common.Ioc;

namespace YYBM.Common.Service.Caches.Action
{
    public class ActionConfigCache
    {
        private readonly static IActionConfigRepository _actionConfigRepository;

        static ActionConfigCache()
        {
            _actionConfigRepository = HttpContextServiceLocator.GetRequiredService<IActionConfigRepository>();
        }

        public static List<ActionConfigModel> GetList()
        {
            var actionConfigs = CacheManager.Redis().GetJson<List<ActionConfigModel>>(CacheKey.ActionConfigList());
            if (actionConfigs == null)
            {
                actionConfigs = _actionConfigRepository
                    .GetAll()
                    .ToList();
                CacheManager.Redis().SetJson(CacheKey.ActionConfigList(), actionConfigs, DateTime.Now.AddHours(60));
            }
            return actionConfigs;
        }

        public static ActionConfigModel Get(string actionFlag)
        {
            var actionConfig = GetList();
            if (actionConfig == null || actionConfig.Count == 0)
                return null;

            return actionConfig.SingleOrDefault(o => o.ActionFlag == actionFlag);
        }

        public static dynamic GetValue(string actionFlag)
        {
            var actionConfig = Get(actionFlag);
            if (actionConfig == null)
                return null;

            var type = actionConfig.Type;
            switch (type)
            {
                case "string":
                    return actionConfig.Value;
                case "int":
                    if (int.TryParse(actionConfig.Value.ToString(), out var intValue))
                        return intValue;
                    break;
                case "bool":
                    if (bool.TryParse(actionConfig.Value.ToString(), out var boolValue))
                        return boolValue;
                    break;
                case "json":
                    return GetActionList(actionFlag);
                default:
                    break;
            }
            return null;
        }

        public static List<AppAction> GetActionList(string actionFlag)
        {
            var actionConfig = Get(actionFlag);
            if (actionConfig == null)
                return null;

            return Json.ToList<AppAction>(actionConfig.Value);
        }

        public static Dictionary<string, AppAction> GetActionDict(string actionFlag)
        {
            var actionConfig = Get(actionFlag);
            if (actionConfig == null)
                return new Dictionary<string, AppAction>();

            var warningActions = Json.ToList<AppAction>(actionConfig.Value);
            if (warningActions == null || !warningActions.Any())
                return new Dictionary<string, AppAction>();

            // 使用字典缓存配置，避免重复查询
            var configDict = warningActions.ToDictionary(x => x.Key, x => x);
            return configDict;
        }

        public static AppAction GetConfigValue(string actionFlag, string key)
        {
            var configDict = GetActionDict(actionFlag);
            return configDict.TryGetValue(key, out var value) ? value : null;
        }

        public static bool ClearCache()
        {
            return CacheManager.Redis().Remove(CacheKey.ActionConfigList());
        }
    }
}
