import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/projectConfig/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/projectConfig/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addProjectConfig(data) {
  return request({
    url: '/projectConfig/add',
    method: 'post',
    data: data,
  })
}

export function editProjectConfig(data) {
  return request({
    url: '/projectConfig/edit',
    method: 'post',
    data: data,
  })
}

export function delProjectConfig(id) {
  return request({
    url: '/projectConfig/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------ProjectConfig结束----------
