﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Cloud
{
    [Route("api/[controller]")]
    [ApiController]
    public class AccountController : BaseApiController
    {
        private readonly IAccountService _accountService;

        public AccountController(IAccountService accountService)
        {
            _accountService = accountService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("account:list")]
        public TableResult<AccountVO> GetList([FromQuery] AccountParams searchParams)
        {
            return _accountService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("account:detail")]
        public ResponseResult<AccountVO> GetDetail([FromQuery] int id)
        {
            return new ResponseResult<AccountVO>(_accountService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("account:add")]
        public ResponseResult Add([FromBody] AccountParams accountParams)
        {
            return _accountService.AddAccount(accountParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("account:edit")]
        public ResponseResult Edit([FromBody] AccountParams accountParams)
        {
            return _accountService.UpdateAccount(accountParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("account:delete")]
        public ResponseResult Delete([FromBody] AccountParams accountParams)
        {
            return _accountService.DeleteAccount(accountParams);
        }
    }
}
