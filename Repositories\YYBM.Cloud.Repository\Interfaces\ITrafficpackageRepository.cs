//----------Trafficpackage开始----------

using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.IRepository
{
    /// <summary>
    /// ITrafficpackageRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:24 
    /// </summary>	
    public interface ITrafficpackageRepository : IBaseRepository<TrafficpackageModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<TrafficpackageModel> SelectPage(TrafficpackageParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertTrafficpackage(TrafficpackageModel trafficpackage);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateTrafficpackage(TrafficpackageModel trafficpackage);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteTrafficpackage(int Id);

        Task<TrafficpackageModel> SelectAsyncByAccount(int accountId);
    }
}

//----------Trafficpackage结束----------  
