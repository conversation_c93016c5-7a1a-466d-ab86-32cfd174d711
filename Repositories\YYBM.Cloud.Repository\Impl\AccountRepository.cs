//----------Account开始----------   

using Microsoft.Extensions.Configuration;
using System.Text;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.IRepository;
using YYBM.Repository.SqlDb;
using ZProjectBase.DB.Extend;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.Repository
{
    /// <summary>
    /// AccountRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:25 
    /// </summary>	
    public class AccountRepository : YYBMCloudRepository<AccountModel>, IAccountRepository
    {
        public AccountRepository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<AccountModel> SelectPage(AccountParams searchParams, PageInfo pageInfo)
        {
            var whereSql = new StringBuilder();
            whereSql.AppendSqlIf("and [Platform] = @Platform", searchParams.Platform > 0);
            var orderBy = "order by Id asc";

            var pageResult = new PageResult<AccountModel>();
            long total;
            pageResult.Data = conn.GetByPage<AccountModel>(pageInfo.page, pageInfo.limit, out total, param: searchParams, where: whereSql.ToWhereSql(), orderBy: orderBy);
            pageResult.Count = total;
            return pageResult;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public bool InsertAccount(AccountModel account)
        {
            var res = conn.Insert(account) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool UpdateAccount(AccountModel account)
        {
            return conn.UpdateById(account) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool DeleteAccount(int Id)
        {
            return conn.DeleteById<AccountModel>(Id) > 0;
        }
    }
}

//----------Account结束----------

