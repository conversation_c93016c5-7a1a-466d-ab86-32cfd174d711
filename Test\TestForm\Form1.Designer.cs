﻿namespace TestForm
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBox1 = new GroupBox();
            btn_DescribeTrafficPackagesApi = new Button();
            btn_DescribeHttpsPackagesApi = new Button();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(btn_DescribeHttpsPackagesApi);
            groupBox1.Controls.Add(btn_DescribeTrafficPackagesApi);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(787, 181);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "腾讯云接口";
            // 
            // btn_DescribeTrafficPackagesApi
            // 
            btn_DescribeTrafficPackagesApi.Location = new Point(6, 22);
            btn_DescribeTrafficPackagesApi.Name = "btn_DescribeTrafficPackagesApi";
            btn_DescribeTrafficPackagesApi.Size = new Size(130, 26);
            btn_DescribeTrafficPackagesApi.TabIndex = 0;
            btn_DescribeTrafficPackagesApi.Text = "获取CDN资源包";
            btn_DescribeTrafficPackagesApi.UseVisualStyleBackColor = true;
            btn_DescribeTrafficPackagesApi.Click += btn_DescribeTrafficPackagesApi_Click;
            // 
            // btn_DescribeHttpsPackagesApi
            // 
            btn_DescribeHttpsPackagesApi.Location = new Point(142, 22);
            btn_DescribeHttpsPackagesApi.Name = "btn_DescribeHttpsPackagesApi";
            btn_DescribeHttpsPackagesApi.Size = new Size(130, 26);
            btn_DescribeHttpsPackagesApi.TabIndex = 1;
            btn_DescribeHttpsPackagesApi.Text = "获取HTTPS资源包";
            btn_DescribeHttpsPackagesApi.UseVisualStyleBackColor = true;
            btn_DescribeHttpsPackagesApi.Click += btn_DescribeHttpsPackagesApi_Click;
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 450);
            Controls.Add(groupBox1);
            Name = "Form1";
            Text = "测试窗体";
            groupBox1.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private GroupBox groupBox1;
        private Button btn_DescribeHttpsPackagesApi;
        private Button btn_DescribeTrafficPackagesApi;
    }
}
