﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Scrutor" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Project\YYLeagueSupplier\SDK\ZProjectBase.Common.Core.Net8\ZProjectBase.Common.Core.csproj" />
    <ProjectReference Include="..\..\..\..\Project\YYLeagueSupplier\SDK\ZProjectBase.DB.SqlServer\ZProjectBase.DB.SqlServer.csproj" />
    <ProjectReference Include="..\..\SDK\TencentCloud\TencentCloud.csproj" />
  </ItemGroup>

</Project>