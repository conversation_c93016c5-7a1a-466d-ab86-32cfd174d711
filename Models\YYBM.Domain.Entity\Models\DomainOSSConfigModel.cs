using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Domain.Entity.Models
{
    ///<summary>
    ///DomainOSSConfig
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-15 19:38:49 
    ///</summary>
    [Table("DomainOSSConfig")]
    public partial class DomainOSSConfigModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(true)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// OSS桶保持个数
        /// </summary>
        public int? KeepCount { get; set; }

        /// <summary>
        /// 下线等待删除时间
        /// </summary>
        public int? OfflineDeleteTime { get; set; }

        /// <summary>
        /// 封禁等待删除时间
        /// </summary>
        public int? BannedDeleteTime { get; set; }

        /// <summary>
        /// 上线域名组Id
        /// </summary>
        public int? DomainGroupId { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        [Required]
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }
    }
}

