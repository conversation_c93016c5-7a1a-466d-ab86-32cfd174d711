﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","VueEdit");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}

    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
    foreach (var item in DbHelper.GetDbTablesNew(config.ConnectionString, config.DbDatabase,config.TableName))
    {
        var tableName=item.ToString();
        var camelCaseName = tableName[0].ToString().ToLower() + tableName.Substring(1);
        manager.StartBlock(tableName+"VueEdit"+".vue",outputPath);
#>

<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close">

    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
<# foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName)){
        var camelName = column.ColumnName.ToString()[0].ToString().ToLower() + column.ColumnName.ToString().Substring(1);
        var columnComment = column.Remark == "" ? column.ColumnName : column.Remark.Replace("\r\n"," ");
#>
		<el-col :span="24">
            <el-form-item label="<#=camelName#>" prop="<#=camelName#>">
<#              if(column.IsPrimaryKey) 
                {#>
                    <el-input :disabled="true" v-model="form.<#=camelName#>" placeholder="请输入<#=columnComment#>" />
<#              }#>
<#              else
                {#>
                    <el-input v-model="form.<#=camelName#>" placeholder="请输入<#=columnComment#>" />
<#              }#>
            </el-form-item>
        </el-col>
<#
}
#> 
      </el-row>
    </el-form>

  </x-dialog>
</template>

<script>
import { <#=camelCaseName#>Api } from '@/api'
export default {
  data () {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
    }
  },
  methods:{
    add () {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit (row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await <#=camelCaseName#>Api.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset () {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm () {
      this.$refs['form'].validate(async valid => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await <#=camelCaseName#>Api.edit<#=tableName#>(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await <#=camelCaseName#>Api.add<#=tableName#>(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    }
  }
}
</script>

<style>

</style>
<#
   manager.EndBlock(); 
   }
   manager.Process(true);
#>