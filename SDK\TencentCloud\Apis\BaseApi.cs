﻿using Newtonsoft.Json;
using TencentCloud.Utils;

namespace TencentCloud.Apis
{
    public class BaseApi
    {
        protected static HttpClient Client { get; set; }
        protected int _httpTimeout = 10 * 1000;

        public BaseApi()
        {
            if (Client == null)
            {
                Client = new HttpClient
                {
                    Timeout = TimeSpan.FromMilliseconds(_httpTimeout)
                };
            }
        }

        protected async Task<string> DoRequest(string secretId, string secretKey, string service, string version, string action, string body, string region, string token)
        {
            var request = TencentCloudUtils.BuildRequest(secretId, secretKey, service, version, action, body, region, token);
            var response = await Client.SendAsync(request);
            return await response.Content.ReadAsStringAsync();
        }

        protected T Deserialize<T>(string str)
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(str);
            }
            catch { }
            return default(T);
        }
    }
}
