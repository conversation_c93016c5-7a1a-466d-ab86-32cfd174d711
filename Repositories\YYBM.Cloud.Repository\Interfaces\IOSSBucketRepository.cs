//----------OSSBucket开始----------

using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.IRepository
{
    /// <summary>
    /// IOSSBucketRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 19:15:39 
    /// </summary>	
    public interface IOSSBucketRepository : IBaseRepository<OSSBucketModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<OSSBucketModel> SelectPage(OSSBucketParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 查询
        /// </summary>
        OSSBucketModel SelectOSSBucket(string Bucket);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertOSSBucket(OSSBucketModel oSSBucket);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateOSSBucket(OSSBucketModel oSSBucket);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteOSSBucket(string Bucket);

        Task<IEnumerable<OSSBucketModel>> GetListAsync(OSSBucketParams searchParam);

        bool UpdateStatus(string bucket, BucketStatus status);
    }
}

//----------OSSBucket结束----------  
