<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @close="onClose"
    width="900px"
  >
    <el-divider content-position="left">{{ subTitle }}</el-divider>

    <el-row :gutter="20">
      <el-col :span="12" v-if="oldValue">
        <div class="waring">
          <json-viewer :value="oldValue" boxed copyable></json-viewer>
        </div>
      </el-col>
      <el-col :span="12" v-if="newValue">
        <div class="success">
          <json-viewer :value="newValue" boxed copyable></json-viewer>
        </div>
      </el-col>

      <el-col :span="24" v-if="desc">
        <div class="waring">
          <json-viewer :value="desc" boxed copyable></json-viewer>
        </div>
      </el-col>
    </el-row>

    <template v-if="isFailure">
      <el-divider content-position="left">异常信息</el-divider>
      <el-col :span="24" v-if="desc">
        <div class="waring">
          <pre>
            异常信息：{{ exception.msg }}
            异常类型：{{ exception.type }}
            异常堆栈：{{ exception.stackTrace }}
          </pre>
        </div>
      </el-col>
    </template>

    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { OperateType } from '@/utils/constant'

import JsonViewer from 'vue-json-viewer'

export default {
  components: {
    JsonViewer,
  },
  data() {
    return {
      title: '详情',
      visible: false,
      loading: false,
      subTitle: '',
      oldValue: '',
      newValue: '',
      isFailure: false,
      desc: '',
      exception: {},
    }
  },
  methods: {
    show(row) {
      this.visible = true
      if (row.status != '0') {
        this.isFailure = true
        this.subTitle = '请求数据'
        if (row.desc) {
          this.desc = JSON.parse(row.desc)
        }
        this.exception.msg = row.exceptionMsg
        this.exception.type = row.exceptionType
        this.exception.stackTrace = row.exceptionStackTrace
        return
      }
      if (row.operateType == OperateType.Add) {
        this.subTitle = '新增的数据'
        this.newValue = JSON.parse(row.newValue)
      } else if (row.operateType == OperateType.Edit) {
        this.subTitle = '修改的数据'
        if (row.oldValue) {
          this.oldValue = JSON.parse(row.oldValue)
        }
        this.newValue = JSON.parse(row.newValue)
      } else {
        this.subTitle = '详情'
        this.subTitle = '无'
      }
    },

    onClose() {
      this.oldValue = undefined
      this.newValue = undefined
      this.desc = undefined
      this.isFailure = false
    },
  },
}
</script>

<style lang="scss" scoped>
.waring {
  background-color: #fdf6ec;
}
.success {
  background-color: #f0f9eb;
}

pre {
  overflow-y: hidden;
}
</style>

<style lang="scss">
.jv-container.jv-light {
  background-color: transparent;
}
</style>
