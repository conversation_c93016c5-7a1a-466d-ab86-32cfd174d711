﻿using Quartz;
using YYBM.Cloud.Service.ObjectStorage.Tasks;
using ZProjectBase.Common.ITask;

namespace YYBM.WS.Cloud.Jobs
{
    public class ObjectStroageAutomationJob : IJob
    {
        protected readonly ObjectStroageAutomationTask _objectStroageAutomationTask;
        private readonly ILogger<ObjectStroageAutomationJob> _logger;

        public ObjectStroageAutomationJob(ObjectStroageAutomationTask objectStroageAutomationTask, ILogger<ObjectStroageAutomationJob> logger)
        {
            _objectStroageAutomationTask = objectStroageAutomationTask;
            _logger = logger;
        }

        public async Task Execute(IJobExecutionContext context)
        {
            _logger.LogInformation("ObjectStroageAutomationJob starting at: {time}", DateTimeOffset.Now);
            try
            {
                await new TaskAsyncBase("ObjectStroageAutomationTask", _objectStroageAutomationTask).StartAsync();

                _logger.LogInformation("ObjectStroageAutomationJob finished successfully at: {time}", DateTimeOffset.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while executing ObjectStroageAutomationJob.");
                // 抛出 JobExecutionException 可以让 Quartz 知道任务失败，并根据配置进行重试等操作
                throw new JobExecutionException(ex);
            }
        }
    }
}
