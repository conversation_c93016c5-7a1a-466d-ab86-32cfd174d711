﻿using Microsoft.Extensions.Configuration;
using ZProjectBase.DB.SqlServer.DB.SQLExts;
using ZProjectBase.DB.SqlServer.Repository;

namespace YYBM.Repository.SqlDb
{
    public class YYBMRepository<T> : SqlServerBaseRepository<T> where T : class, new()
    {
        public YYBMRepository(IConfiguration configuration)
        {
            string? connstr = configuration.GetConnectionString("YYBMConnstr");
            conn = new SqlServerExt(connstr);
        }
    }
}
