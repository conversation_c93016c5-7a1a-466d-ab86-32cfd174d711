//----------ProjectConfig开始----------

using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.IRepository
{
    /// <summary>
    /// IProjectConfigRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 18:12:13 
    /// </summary>	
    public interface IProjectConfigRepository : IBaseRepository<ProjectConfigModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<ProjectConfigModel> SelectPage(ProjectConfigParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertProjectConfig(ProjectConfigModel projectConfig);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateProjectConfig(ProjectConfigModel projectConfig);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteProjectConfig(int Id);
    }
}

//----------ProjectConfig结束----------  
