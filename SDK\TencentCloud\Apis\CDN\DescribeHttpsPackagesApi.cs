﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TencentCloud.Models.Response.CDN;

namespace TencentCloud.Apis.CDN
{
    /// <summary>
    /// 查询Https资源包
    /// </summary>
    public class DescribeHttpsPackagesApi : BaseApi
    {
        public async Task<DescribeHttpsPackagesResponse> GetDescribeHttpsPackages(string secretId, string secretKey)
        {
            var token = "";
            var service = "cdn";
            var version = "2018-06-06";
            var action = "DescribeHttpsPackages";
            var body = "{}";
            var region = "";
            var resp = await DoRequest(secretId, secretKey, service, version, action, body, region, token);
            return Deserialize<DescribeHttpsPackagesResponse>(resp);
        }
    }
}
