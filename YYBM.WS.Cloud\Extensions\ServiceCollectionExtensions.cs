﻿using System.Reflection;

namespace YYBM.WS.Cloud.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static void AddCustomServices(this IServiceCollection services, Assembly[] assemblies)
        {
            services.Scan(scan => scan
                .FromAssemblies(assemblies)
                .AddClasses(c => c.Where(t =>
                    t.Name.EndsWith("Service") ||
                    t.Name.EndsWith("Repository")))
                .AsImplementedInterfaces()
                .WithTransientLifetime());
        }

        public static void AddUtils(this IServiceCollection services, Assembly[] assemblies)
        {
            services.Scan(scan => scan
                .FromAssemblies(assemblies)
                .AddClasses()
                .AsSelfWithInterfaces()
                .WithTransientLifetime());
        }
    }
}
