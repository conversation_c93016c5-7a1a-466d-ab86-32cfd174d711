//----------OSSConfig开始----------

using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// OSSConfigService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 16:15:15 
    /// </summary>	
    public interface IOSSConfigService : IBaseService<OSSConfigModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<OSSConfigVO> GetTableList(OSSConfigParams searchParams);

        /// <summary>
        /// 查询
        /// </summary>
        OSSConfigVO GetDetail(string Id);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddOSSConfig(OSSConfigParams oSSConfigParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateOSSConfig(OSSConfigParams oSSConfigParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteOSSConfig(OSSConfigParams oSSConfigParams);

    }
}

//----------OSSConfig结束----------
