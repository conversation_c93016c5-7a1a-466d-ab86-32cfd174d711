﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using TencentCloud.Apis.CDN;
using ZProjectBase.Common.Cache.Options;

namespace TestForm.Extensions
{
    public class CreateHostBuilderExtensions
    {
        // 建议将方法重命名并更改返回类型，以反映其现代化实现
        public static HostApplicationBuilder CreateApplicationBuilder()
        {
            // 1. 使用 CreateApplicationBuilder，它返回一个集成了配置、服务和日志的对象
            var builder = Host.CreateApplicationBuilder();
            // 2. 配置部分：直接在 builder.Configuration 上操作
            // 注意：CreateApplicationBuilder 默认就已经做了以下事情：
            //   - 将基路径设置为 AppContext.BaseDirectory
            //   - 加载 appsettings.json 和 appsettings.{Environment}.json
            //   - 加载环境变量
            // 因此，下面的代码主要是为了确保您的特定设置（如 optional: false）得到应用。
            builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            // 3. 服务注册部分：直接在 builder.Services 上操作
            // 注册 Service,Repository
            var assemblies = new[]
            {
                typeof(Program).Assembly, // 当前程序集
                //typeof(SystemUserService).Assembly,         // 你的服务层程序集
                //typeof(SystemUserRepository).Assembly,      // 你的数据层程序集                       
            };
            builder.Services.AddCustomServices(assemblies);

            //// 注册 Utils 方法
            //builder.Services.AddUtils(new[] {
            //    //typeof(LoginUtils).Assembly,
            //    typeof(DescribeHttpsPackagesApi).Assembly,                 // SDK.WXShop.Apis

            //});
            //// 注册数据库服务
            // builder.Services.AddDatabase(builder.Configuration); // 直接使用 builder.Configuration
            // 注册 Redis 配置

            builder.Services.Configure<RedisOptions>(
                builder.Configuration.GetSection(RedisOptions.SectionName) // 直接使用 builder.Configuration
            );

            // 注册窗体
            builder.Services.AddTransient<Form1>();
            // 4. 不再需要手动注册日志和控制台生命周期
            // builder.Logging.AddConsole() 和 .UseConsoleLifetime()
            // 都是 CreateApplicationBuilder 的默认行为，可以安全地移除。
            return builder;
        }
    }
}
