﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Cloud
{
    [Route("api/[controller]")]
    [ApiController]
    public class OSSConfigController : BaseApiController
    {
        private readonly IOSSConfigService _ossConfigService;

        public OSSConfigController(IOSSConfigService ossConfigService)
        {
            _ossConfigService = ossConfigService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("ossConfig:list")]
        public TableResult<OSSConfigVO> GetList([FromQuery] OSSConfigParams searchParams)
        {
            return _ossConfigService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("ossConfig:detail")]
        public ResponseResult<OSSConfigVO> GetDetail([FromQuery] string id)
        {
            return new ResponseResult<OSSConfigVO>(_ossConfigService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("ossConfig:add")]
        public ResponseResult Add([FromBody] OSSConfigParams ossConfigParams)
        {
            return _ossConfigService.AddOSSConfig(ossConfigParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("ossConfig:edit")]
        public ResponseResult Edit([FromBody] OSSConfigParams ossConfigParams)
        {
            return _ossConfigService.UpdateOSSConfig(ossConfigParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("ossConfig:delete")]
        public ResponseResult Delete([FromBody] OSSConfigParams ossConfigParams)
        {
            return _ossConfigService.DeleteOSSConfig(ossConfigParams);
        }
    }
}
