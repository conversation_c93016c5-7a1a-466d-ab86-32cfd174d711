﻿using ECloud.Apis.EOS;
using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.ObjectStorage.Interfaces;
using YYBM.Cloud.Service.ObjectStorage.DTOs;
using YYBM.Cloud.Service.ObjectStorage.Interfaces;
using ZProjectBase.Common.Ioc;
using ZProjectBase.Common.Utils;

namespace YYBM.Cloud.Service.ObjectStorage.Providers.ECloud
{
    public class ECloudClient : IObjectStorageService
    {
        private readonly ECloudAccountProvider _eCloudAccountProvider = HttpContextServiceLocator.GetService<ECloudAccountProvider>();

        #region Bucket

        public IEnumerable<Bucket> GetBuckets(AccountVO accountVO)
        {
            var bucketApi = BucketApiProvider(accountVO);
            var buckets = bucketApi.ListBuckets();
            if (buckets == null || buckets.HttpStatusCode != System.Net.HttpStatusCode.OK)
            {
                throw new Exception("Failed to retrieve buckets from Aliyun OSS.");
            }
            var bucketList = MapperUtil.MapperIns.Map<IEnumerable<Bucket>>(buckets.Buckets);
            return bucketList;
        }

        public bool CreateBucket(string bucketName, AccountVO accountVO)
        {
            if (DoesBucketExist(bucketName, accountVO))
                return false;

            var bucketApi = BucketApiProvider(accountVO);
            var bucket = bucketApi.CreateBucket(bucketName);
            return bucket != null && bucket.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }

        public bool DeleteBucket(string bucketName, AccountVO accountVO)
        {
            if (DoesBucketExist(bucketName, accountVO))
                return true;

            var bucketApi = BucketApiProvider(accountVO);
            return bucketApi.DeleteBucket(bucketName);
        }
        public bool DoesBucketExist(string bucketName, AccountVO accountVO)
        {
            var bucketApi = BucketApiProvider(accountVO);
            return bucketApi.HeadBucket(bucketName);
        }

        public bool SetBucketWebsite(string bucektName, string indexDocument, string errorDocument, AccountVO accountVO)
        {
            var bucketApi = BucketApiProvider(accountVO);
            return bucketApi.SetBucketWebsite(bucektName, indexDocument, errorDocument);
        }

        private BucketApi BucketApiProvider(AccountVO accountVO)
        {
            if (accountVO == null)
            {
                accountVO = _eCloudAccountProvider.GetRamdonAccount(CloudPlatform.Aliyun);
            }
            return new BucketApi(accountVO.SecretId, accountVO.SecretKey, accountVO.Endpoint);
        }

        #endregion

        #region Object

        public IEnumerable<OssObject> GetObjects(string bucketName, AccountVO accountVO)
        {
            var objectApi = ObjectApiProvider(accountVO);
            var objects = objectApi.ListObjects(bucketName);
            if (objects == null || !objects.S3Objects.Any())
            {
                throw new Exception("No objects found in the specified bucket.");
            }
            return MapperUtil.MapperIns.Map<IEnumerable<OssObject>>(objects);
        }

        public bool PutObjectFromFile(string bucketName, string key, string filePath, AccountVO accountVO)
        {
            var objectApi = ObjectApiProvider(accountVO);
            return objectApi.PutObjectFromFile(bucketName, key, filePath);
        }

        public OssObject GetObject(string bucketName, string objectKey, AccountVO accountVO)
        {
            var objectApi = ObjectApiProvider(accountVO);
            var ossObject = objectApi.GetObject(bucketName, objectKey);
            if (ossObject == null)
            {
                throw new Exception($"Object with key '{objectKey}' not found in bucket '{bucketName}'.");
            }
            return MapperUtil.MapperIns.Map<OssObject>(ossObject);
        }

        public bool DeleteObject(string bucketName, string key, AccountVO accountVO)
        {
            var objectApi = ObjectApiProvider(accountVO);
            return objectApi.DeleteObject(bucketName, key);
        }

        public bool DeleteObjects(string bucketName, AccountVO accountVO)
        {
            var objectApi = ObjectApiProvider(accountVO);
            return objectApi.DeleteObjects(bucketName);
        }

        public bool DoesObjectExist(string bucketName, string key, AccountVO accountVO)
        {
            throw new NotImplementedException("DoesObjectExist method is not implemented for ECloud.");
        }

        private ObjectApi ObjectApiProvider(AccountVO accountVO)
        {
            if (accountVO == null)
            {
                accountVO = _eCloudAccountProvider.GetRamdonAccount(CloudPlatform.Aliyun);
            }
            return new ObjectApi(accountVO.SecretId, accountVO.SecretKey, accountVO.Endpoint);
        }

        IEnumerable<Bucket> IObjectStorageBucket.GetBuckets(AccountVO accountVO)
        {
            throw new NotImplementedException();
        }

        IEnumerable<OssObject> IObjectStorageObject.GetObjects(string bucketName, AccountVO accountVO)
        {
            throw new NotImplementedException();
        }

        OssObject IObjectStorageObject.GetObject(string bucketName, string objectKey, AccountVO accountVO)
        {
            throw new NotImplementedException();
        }

        #endregion
    }
}
