<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude" #>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<#
    var outputPath = Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
    outputPath = Path.Combine(outputPath, OutputDllPath, "Temp", "IEFRepository");
    if (!Directory.Exists(outputPath)) Directory.CreateDirectory(outputPath);
#>

//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//     生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#>
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# var tableName=config.TableName; #>
<# if(tableName!=""){ #>
using ZProjectBase.DB.IRepository;
using <#=ProjectName#>.Entity.Models;
using ZProjectBase.Mvc;

namespace <#=ProjectName#>.IRepository
{
    /// <summary>
    /// I<#=tableName#>Repository (EF)
    /// </summary>
    public partial interface I<#=tableName#>Repository : IBaseRepository<<#=tableName#>>
    {
        PageResult<<#=tableName#>> SelectPage(<#=tableName#>Params searchParams, PageInfo pageInfo);
        <#
            var primaryKey = "";
            var cSharpType = "";
            foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName))
            {
                if(column.IsPrimaryKey) { cSharpType = column.CSharpType; primaryKey = column.ColumnName[0].ToString().ToLower() +  column.ColumnName.Substring(1); break; }
            }
            var camelCaseName = tableName[0].ToString().ToLower() + tableName.Substring(1);
            var modelName = tableName + "Model";
        #>
        <# if(cSharpType != "int"){ #>
        <#=modelName#> Select<#=tableName#>(<#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey==""?"id":primaryKey#>);
        <# } #>
        bool Insert<#=tableName#>(<#=modelName#> <#=camelCaseName#>);
        bool Update<#=tableName#>(<#=modelName#> <#=camelCaseName#>);
        bool Delete<#=tableName#>(<#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey==""?"id":primaryKey#>);
    }
}
<# } else { #>
<#
    using(SqlConnection conn = new SqlConnection(config.ConnectionString)){
        conn.Open();
        var schema = conn.GetSchema("TABLES");
        foreach(DataRow row in schema.Rows){
            manager.StartBlock("I"+row["TABLE_NAME"].ToString()+"Repository"+".cs", outputPath);
            var primaryKey = "";
            var cSharpType = "";
            foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, row["TABLE_NAME"].ToString()))
            {
                if(column.IsPrimaryKey) { cSharpType = column.CSharpType; primaryKey = column.ColumnName[0].ToString().ToLower() +  column.ColumnName.Substring(1); break; }
            }
            var tableNameModel = row["TABLE_NAME"].ToString()+ "Model";
            var paramsName = row["TABLE_NAME"].ToString() + "Params";
            var camelCaseName = row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1);
#>
using ZProjectBase.DB.IRepository;
using <#=ProjectName#>.Entity.Models;
using ZProjectBase.Mvc;

namespace <#=ProjectName#>.IRepository
{
    /// <summary>
    /// I<#=row["TABLE_NAME"].ToString()#>Repository (EF)
    /// </summary>
    public interface I<#=row["TABLE_NAME"].ToString()#>Repository : IBaseRepository<<#=tableNameModel#>>
    {
        PageResult<<#=tableNameModel#>> SelectPage(<#=paramsName#> searchParams, PageInfo pageInfo);
        <# if(cSharpType != "int"){ #>
        <#=tableNameModel#> Select<#=row["TABLE_NAME"].ToString()#>(<#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey==""?"id":primaryKey#>);
        <# } #>
        bool Insert<#=row["TABLE_NAME"].ToString()#>(<#=tableNameModel#> <#=camelCaseName#>);
        bool Update<#=row["TABLE_NAME"].ToString()#>(<#=tableNameModel#> <#=camelCaseName#>);
        bool Delete<#=row["TABLE_NAME"].ToString()#>(<#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey==""?"id":primaryKey#>);
    }
}

//----------<#=row["TABLE_NAME"].ToString()#>结束----------
<#
            manager.EndBlock();
        }
        manager.Process(true);
    }
#>
<# } #>

