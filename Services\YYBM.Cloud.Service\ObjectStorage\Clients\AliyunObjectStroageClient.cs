﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;
using YYBM.Cloud.ObjectStorage.Interfaces;
using YYBM.Cloud.Service.ObjectStorage.Abstractions;
using YYBM.Cloud.Service.ObjectStorage.Mappers;
using YYBM.Cloud.Service.ObjectStorage.Providers.Aliyun;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.Entity.VOs;
using YYBM.Domain.IRepository;
using YYBM.Domain.Repository;
using ZProjectBase.Common.Extend;
using ZProjectBase.Common.Log;

namespace YYBM.Cloud.Service.ObjectStorage.Clients
{
    public class AliyunObjectStroageClient : BaseObjectStorageClient
    {
        protected override string DomainTemplate { get; } = "http://{0}.{1}/{2}";
        protected override IObjectStorageService _ossClient => AliyunCreator.GetInstance();
        protected override CloudPlatform _platform => CloudPlatform.Aliyun;

        private readonly IDomainRepository _domainRepository;
        private readonly IDomianOSSRelevanceRepository _domianOSSRelevanceRepository;

        public AliyunObjectStroageClient(
            IOSSConfigRepository ossConfigRepository,
            IOSSBucketRepository ossBucketRepository,
            IDomainOSSConfigRepository domainOSSConfigRepository,
            IAccountRepository accountRepository,
            IDomainRepository domainRepository,
            IDomianOSSRelevanceRepository domianOSSRelevanceRepository)
            : base(
                  ossConfigRepository,
                  ossBucketRepository,
                  domainOSSConfigRepository,
                  accountRepository)
        {
            _domainRepository = domainRepository;
            _domianOSSRelevanceRepository = domianOSSRelevanceRepository;
        }

        public override async Task<bool> UploadFileOrCreateDomainAsync(string bucketName, string fileName, OSSConfigModel ossConfig, AccountVO accountVO, FilesPathConfig filesPathConfig)
        {
            var uploadFileResult = UploadFile(bucketName, fileName, filesPathConfig.Path, accountVO);
            if (!uploadFileResult)
            {
                L4NLog.Warn($"Failed to upload file: {filesPathConfig.Path} to bucket: {bucketName}");
                return false;
            }

            if (!filesPathConfig.IsCreateDomain)
                return false;

            string domain;
            if (ossConfig.WebsiteFilePath.IsNotNullOrEmpty() && filesPathConfig.UrlPath.IsNullOrEmpty())
            {
                domain = string.Format(DomainTemplate, bucketName, accountVO.Endpoint, "");
            }
            else if (filesPathConfig.UrlPath.IsNullOrEmpty())
            {
                domain = string.Format(DomainTemplate, bucketName, accountVO.Endpoint, filesPathConfig.Key);
            }
            else
            {
                domain = string.Format(DomainTemplate, bucketName, accountVO.Endpoint, filesPathConfig.UrlPath);
            }

            var domainOssConfig = await _domainOSSConfigRepository.SelectAsync(ossConfig.DomainOssConfigId);

            return await _domainOSSConfigRepository.ExecuteInTransactionAsync(async (con, tran) =>
            {
                var domainModel = new DomainModel
                {
                    ProjId = ossConfig.ProjId,
                    GroupId = domainOssConfig?.DomainGroupId.Value ?? -1,
                    Domain = domain,
                    Weight = 10,
                    State = Domain.Entity.Enums.DomainState.Offline,
                    CreateOn = DateTime.Now,
                    IsRandomSLD = false,
                    IsOSS = true,
                };

                var domainId = await _domainRepository.CreateAsync(con, tran, domainModel);

                var domainOssRelevance = new DomianOSSRelevanceModel()
                {
                    Bucket = bucketName,
                    DomainId = domainId,
                    Platform = (int)_platform,
                };

                var result = await _domianOSSRelevanceRepository.CreateAsync(con, tran, domainOssRelevance) > 0;

                return domainId > 0 && result;
            });
        }

        public override async Task CreateStaticWebsitAsync(string bucketName, OSSConfigModel ossConfig, AccountVO accountVO)
        {
            await Task.Run<bool>(() =>
            {
                if (ossConfig.WebsiteFilePath.IsNullOrEmpty())
                    return false;

                string indexFileName = "index.html", errorFileName = "error.html";

                var indexUploadFileResult = UploadFile(bucketName, indexFileName, ossConfig.WebsiteFilePath, accountVO);
                var errorUploadFileResult = UploadFile(bucketName, errorFileName, ossConfig.WebsiteFilePath, accountVO);

                if (!indexUploadFileResult || !errorUploadFileResult)
                {
                    L4NLog.Warn($"Failed to upload static website files for bucket: {bucketName}");
                    return false;
                }


                return _ossClient.SetBucketWebsite(bucketName, indexFileName, errorFileName, accountVO);
            });
        }
    }
}
