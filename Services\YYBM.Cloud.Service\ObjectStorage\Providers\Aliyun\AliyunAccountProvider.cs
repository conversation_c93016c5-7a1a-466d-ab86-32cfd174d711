﻿using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;

namespace YYBM.Cloud.Service.ObjectStorage.Providers.Aliyun
{
    public class AliyunAccountProvider : AccountProvider
    {
        private readonly IAccountRepository _accountRepository;

        public AliyunAccountProvider(IAccountRepository accountRepository) : base(accountRepository)
        {
            _accountRepository = accountRepository;
        }

        public AccountVO GetAccount(int? id)
        {
            if (id.HasValue)
            {
                return GetAccount(id.Value);
            }
            else
            {
                return GetRamdonAccount(CloudPlatform.Aliyun);
            }
        }
    }
}
