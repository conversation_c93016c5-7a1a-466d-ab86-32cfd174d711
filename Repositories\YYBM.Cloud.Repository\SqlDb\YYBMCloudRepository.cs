﻿using Microsoft.Extensions.Configuration;
using ZProjectBase.DB.SqlServer.DB.SQLExts;
using ZProjectBase.DB.SqlServer.Repository;

namespace YYBM.Repository.SqlDb
{
    public class YYBMCloudRepository<T> : SqlServerBaseRepository<T> where T : class, new()
    {
        public YYBMCloudRepository(IConfiguration configuration)
        {
            string? connstr = configuration.GetConnectionString("YYBMCloudConnstr");
            conn = new SqlServerExt(connstr);
        }
    }
}
