<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    width="1000px"
    @submit="submitForm"
    @cancel="visible = false"
    append-to-body
  >
    <!-- 预览、排序 -->
    <h3 class="table-title">选中横幅</h3>
    <span class="table-desc">拖动可排序</span>
    <div class="banner-preview">
      <template v-if="selectedBanners.length">
        <draggable v-model="selectedBanners">
          <div class="item" v-for="(item, index) in selectedBanners" :key="index">
            <el-image class="img" fit="contain" :src="item.imageURL_Url"></el-image>
            <span>ID:{{ item.id }}</span>
            <el-tooltip content="移除横幅" placement="bottom">
              <el-button
                @click="handleDeleteBanner(index)"
                type="danger"
                icon="el-icon-delete"
                size="mini"
                style="padding: 2px;"
              ></el-button>
            </el-tooltip>
          </div>
        </draggable>
      </template>
      <div v-else class="no-data">
        请先在下面的横幅列表勾选需要展示的横幅 ↓↓↓
      </div>
    </div>

    <!-- 表格 -->
    <h3 class="table-title">横幅列表</h3>
    <span class="table-desc">请点击按钮添加需要展示的横幅</span>
    <el-table :data="tableData" :height="300">
      <!-- <el-table-column type="selection" align="center" width="55"></el-table-column> -->
      <el-table-column align="center" label="点击添加">
        <template v-slot="{ row }">
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleAddBanner(row)"
          ></el-button>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="ID" align="center"></el-table-column>
      <el-table-column prop="imageURL_Url" label="图片" align="center">
        <template v-slot="{ row }">
          <el-image
            fit="contain"
            :src="row.imageURL_Url"
            style="width: 80px; height: 45px;"
          ></el-image>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序" align="center"></el-table-column>
    </el-table>
  </x-dialog>
</template>

<script>
import draggable from 'vuedraggable'
import { bannerApi } from '@/api'

export default {
  components: {
    draggable,
  },
  data() {
    return {
      title: '选择横幅',
      visible: false,
      loading: false,
      bannerIds: [], // 选中的banner
      tableData: [],
      selectedBanners: [],
    }
  },
  created() {
    // 阻止火狐浏览器默认的拖拽搜索行为
    document.body.ondrop = (event) => {
      event.preventDefault()
      event.stopPropagation()
    }
  },
  methods: {
    async edit(bannerIds) {
      this.selectedBanners = []
      this.bannerIds = bannerIds || []
      this.visible = true
      this.$xloading.show()
      await this.loadData()
      this.$xloading.hide()
    },
    submitForm() {
      // console.log(this.selectedBanners.map(t => t.id))
      this.$emit('ok', this.selectedBanners)
      this.visible = false
    },
    async loadData() {
      const res = await bannerApi.getAll()
      this.tableData = res.data
    },
    handleAddBanner(row) {
      if (this.selectedBanners.length == 10) {
        this.$xMsgInfo('横幅不能超过10个')
        return
      }
      this.selectedBanners.push(row)
    },
    handleDeleteBanner(index) {
      this.selectedBanners.splice(index, 1)
    },
  },
}
</script>

<style lang="scss" scoped>
.banner-preview {
  height: 200px;
  color: #c0c4cc;
  // background-color: antiquewhite;
  padding: 10px 0;

  $item-width: 120px;
  .item {
    width: $item-width + 10px;
    text-align: center;
    padding: 5px;
    display: inline-block;
    margin: 0 10px;

    &:hover {
      background-color: #ebf7ff;
    }

    .img {
      width: $item-width;
      height: ($item-width * 9/16);
    }
  }
}
.table-title {
  display: inline;
}
.table-desc {
  margin-left: 10px;
  color: #c0c4cc;
}
</style>
