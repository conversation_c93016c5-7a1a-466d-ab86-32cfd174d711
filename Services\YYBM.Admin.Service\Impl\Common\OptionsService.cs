﻿using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.IRepository;
using YYBM.Domain.IRepository;
using YYBM.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Admin.Core.DTOs;
using ZProjectBase.Common.Extend;
using ZProjectBase.Common.Model;

namespace YYBM.Admin.Service
{
    public class OptionsService : AdminServiceBase, IOptionsService
    {
        private readonly IProjectConfigRepository _projectConfigRepository;
        private readonly IDomainGroupRepository _domainGroupRepository;
        private readonly IDomainOSSConfigRepository _domainOSSConfigRepository;
        private readonly IAccountRepository _accountRepository;

        public OptionsService(
            IHttpContextAccessor httpContextAccessor,
            IProjectConfigRepository projectConfigRepository,
            IDomainGroupRepository domainGroupRepository,
            IDomainOSSConfigRepository domainOSSConfigRepository,
            IAccountRepository accountRepository) : base(httpContextAccessor)
        {
            _projectConfigRepository = projectConfigRepository;
            _domainGroupRepository = domainGroupRepository;
            _domainOSSConfigRepository = domainOSSConfigRepository;
            _accountRepository = accountRepository;
        }

        /// <summary>
        /// 获取云平台选项列表
        /// </summary>
        /// <returns></returns>
        public List<OptionItem<int>> GetCloudPlatformOptions()
        {
            return GetOptionListByEnumType(typeof(CloudPlatform));
        }

        /// <summary>
        /// 获取项目选项列表
        /// </summary>
        /// <returns></returns>
        public List<OptionItem<int>> GetProjectOptions()
        {
            var orderBy = "order by Id asc";
            var projectConfigs = _projectConfigRepository.GetAll(orderby: orderBy);
            return projectConfigs.Select(t => new OptionItem<int>()
            {
                Id = t.Id,
                Label = t.Name,
                Value = t.Id,
                Description = t.ParentId == 0 ? "主平台" : "子平台",
                Color = t.ParentId == 0 ? "green" : "red"
            }).ToList();
        }

        /// <summary>
        /// 获取域名分组选项列表
        /// </summary>
        /// <returns></returns>
        public List<OptionItem<int>> GetDomainGroupOptions()
        {
            var orderBy = "order by Id asc";
            var domainGroups = _domainGroupRepository.GetAll(orderby: orderBy);
            return domainGroups.Select(t => new OptionItem<int>()
            {
                Id = t.Id,
                Label = $"{t.Name}({t.Id})",
                Value = t.Id
            }).ToList();
        }

        /// <summary>
        /// 获取域名OSS配置选项列表
        /// </summary>
        /// <returns></returns>
        public List<OptionItem<int>> GetDomainOSSConfigOptions()
        {
            var orderBy = "order by Id asc";
            var domainOSSConfigs = _domainOSSConfigRepository.GetAll(orderby: orderBy);
            return domainOSSConfigs.Select(t => new OptionItem<int>()
            {
                Id = t.Id,
                Label = $"{t.Name}({t.Id})",
                Value = t.Id,
            }).ToList();
        }

        /// <summary>
        /// 通过平台获取云账号选项列表
        /// </summary>
        /// <param name="platform"></param>
        /// <returns></returns>
        public List<OptionGroupItem<int>> GetCloudAccountOptions(int? platform)
        {
            var orderBy = "order by Platform asc, Id asc";
            var accounts = _accountRepository.GetAll(orderby: orderBy);
            if (platform.HasValue)
                accounts = accounts.Where(t => t.Platform == platform.Value);

            var platforms = GetOptionListByEnumType(typeof(CloudPlatform));

            return accounts
              .GroupBy(t => new { t.Platform })
              .Select(g => new OptionGroupItem<int>
              {
                  Label = platforms.FirstOrDefault(o => o.Value == g.Key.Platform)?.Label ?? "未知平台",
                  Options = g.Select(t => new OptionItem<int>
                  {
                      Id = t.Id,
                      Label = t.Name,
                      Value = t.Id
                  }).ToList()
              }).ToList();
        }


        public List<OptionItem<int>> GetBucketStatusOptions()
        {
            return GetOptionListByEnumType(typeof(BucketStatus));
        }

        /// <summary>
        /// 将枚举类型转成OptionItem类型，给前端下拉框等组件使用
        /// </summary>
        /// <param name="enumType"></param>
        /// <returns></returns>
        private List<OptionItem<int>> GetOptionListByEnumType(Type enumType)
        {
            var enumModels = EnumExt.GetEnumModels(enumType);
            return ConvertEnumsToOptionsList(enumModels);
        }

        private List<OptionItem<int>> ConvertEnumsToOptionsList(IEnumerable<EnumModel> enumList)
        {
            var routeTypes = enumList.Select(t => new OptionItem<int>()
            {
                Id = t.Value,
                Label = t.Display,
                Value = t.Value
            });
            return routeTypes.ToList();
        }
    }
}
