//----------Trafficpackage开始----------   

using Microsoft.Extensions.Configuration;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.IRepository;
using YYBM.Repository.SqlDb;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.Repository
{
    /// <summary>
    /// TrafficpackageRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:25 
    /// </summary>	
    public class TrafficpackageRepository : YYBMCloudRepository<TrafficpackageModel>, ITrafficpackageRepository
    {
        public TrafficpackageRepository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<TrafficpackageModel> SelectPage(TrafficpackageParams searchParams, PageInfo pageInfo)
        {
            var pageResult = new PageResult<TrafficpackageModel>();
            long total;
            pageResult.Data = conn.GetByPage<TrafficpackageModel>(pageInfo.page, pageInfo.limit, out total, param: searchParams);
            pageResult.Count = total;
            return pageResult;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public bool InsertTrafficpackage(TrafficpackageModel trafficpackage)
        {
            var res = conn.Insert(trafficpackage) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool UpdateTrafficpackage(TrafficpackageModel trafficpackage)
        {
            return conn.UpdateById(trafficpackage) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool DeleteTrafficpackage(int Id)
        {
            return conn.DeleteById<TrafficpackageModel>(Id) > 0;
        }

        public async Task<TrafficpackageModel> SelectAsyncByAccount(int accountId)
        {
            var whereSql = "where AccountId = @AccountId";
            return await conn.GetByWhereFirstAsync<TrafficpackageModel>(whereSql, new { AccountId = accountId });
        }
    }
}

//----------Trafficpackage结束----------

