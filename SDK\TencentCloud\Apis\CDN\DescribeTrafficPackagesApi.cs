﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TencentCloud.Models.Response.CDN;

namespace TencentCloud.Apis.CDN
{
    /// <summary>
    /// 查询流量包列表
    /// </summary>
    public class DescribeTrafficPackagesApi : BaseApi
    {
        public async Task<DescribeTrafficPackagesResponse> GetDescribeTrafficPackages(string secretId, string secretKey)
        {
            var token = "";
            var service = "cdn";
            var version = "2018-06-06";
            var action = "DescribeTrafficPackages";
            var body = "{}";
            var region = "";
            var resp = await DoRequest(secretId, secretKey, service, version, action, body, region, token);
            return Deserialize<DescribeTrafficPackagesResponse>(resp);
        }
    }
}
