﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Stats
{
    [Route("api/[controller]")]
    [ApiController]
    public class DashBoardController : BaseApiController
    {
        private readonly IDashBoardService _dashBoardService;

        public DashBoardController(IDashBoardService dashBoardService)
        {
            _dashBoardService = dashBoardService;
        }

        [HttpGet("GetCardList")]
        public async Task<ResponseResult<List<DashBoardCardVO>>> GetCardList()
        {
            return await _dashBoardService.GetCardList();
        }
    }
}
