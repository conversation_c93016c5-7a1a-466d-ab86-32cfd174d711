﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.DTOs;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Common
{
    [Route("api/[controller]")]
    [ApiController]
    public class OptionsController : BaseApiController
    {
        private readonly IOptionsService _optionsService;

        public OptionsController(IOptionsService optionsService)
        {
            _optionsService = optionsService;
        }

        [HttpGet("GetCloudPlatformOptions")]
        public ResponseResult<List<OptionItem<int>>> GetCloudPlatformOptions()
        {
            var options = _optionsService.GetCloudPlatformOptions();
            return new ResponseResult<List<OptionItem<int>>>(options);
        }

        [HttpGet("GetProjectOptions")]
        public ResponseResult<List<OptionItem<int>>> GetProjectOptions()
        {
            var options = _optionsService.GetProjectOptions();
            return new ResponseResult<List<OptionItem<int>>>(options);
        }

        [HttpGet("GetDomainGroupOptions")]
        public ResponseResult<List<OptionItem<int>>> GetDomainGroupOptions()
        {
            var options = _optionsService.GetDomainGroupOptions();
            return new ResponseResult<List<OptionItem<int>>>(options);
        }

        [HttpGet("GetDomainOSSConfigOptions")]
        public ResponseResult<List<OptionItem<int>>> GetDomainOSSConfigOptions()
        {
            var options = _optionsService.GetDomainOSSConfigOptions();
            return new ResponseResult<List<OptionItem<int>>>(options);
        }

        [HttpGet("GetCloudAccountOptions")]
        public ResponseResult<List<OptionGroupItem<int>>> GetCloudAccountOptions([FromQuery] int? platform)
        {
            var options = _optionsService.GetCloudAccountOptions(platform);
            return new ResponseResult<List<OptionGroupItem<int>>>(options);
        }

        [HttpGet("GetBucketStatusOptions")]
        public ResponseResult<List<OptionItem<int>>> GetBucketStatusOptions()
        {
            var options = _optionsService.GetBucketStatusOptions();
            return new ResponseResult<List<OptionItem<int>>>(options);
        }
    }
}
