﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace YYBM.Admin.Api.Extensions
{
    public class DateTimeConverter : JsonConverter<DateTime>
    {
        private const string Format = "yyyy-MM-dd HH:mm:ss";

        // 从 JSON 反序列化为 DateTime
        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            // 这里为了简单起见，我们假设输入格式也是固定的。
            // 在生产环境中，您可能需要更复杂的解析逻辑来处理多种格式。
            return DateTime.ParseExact(reader.GetString()!, Format, null);
        }

        // 将 DateTime 序列化为 JSON 字符串
        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            writer.WriteStringValue(value.ToString(Format));
        }
    }
}
