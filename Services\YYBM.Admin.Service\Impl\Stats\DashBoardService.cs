﻿using YYBM.Admin.IService;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    public class DashBoardService : IDashBoardService
    {
        private readonly IAccountRepository _accountRepository;
        private readonly ITrafficpackageRepository _trafficpackageRepository;
        private readonly IHttpspackageRepository _httpspackageRepository;

        public DashBoardService(ITrafficpackageRepository trafficpackageRepository, IHttpspackageRepository httpspackageRepository, IAccountRepository accountRepository)
        {
            _trafficpackageRepository = trafficpackageRepository;
            _httpspackageRepository = httpspackageRepository;
            _accountRepository = accountRepository;
        }

        public async Task<ResponseResult<List<DashBoardCardVO>>> GetCardList()
        {
            var dashBoardCards = new List<DashBoardCardVO>();

            var trafficpackages = await _trafficpackageRepository.GetAllAsync();
            var httpspackages = await _httpspackageRepository.GetAllAsync();

            foreach (var trafficpackage in trafficpackages)
            {
                var account = await _accountRepository.SelectAsync(trafficpackage.AccountId);
                dashBoardCards.Add(new DashBoardCardVO()
                {
                    Title = $"{account?.Name} 流量包详情",
                    Text = $"当前流量包剩余：{trafficpackage?.GB.ToString("N0") ?? "0"} GB",
                    SubText = $"在跑流量包数量：{trafficpackage?.EnableCount ?? 0} 个, 最后检测时间：{trafficpackage?.LastUpdateTime}",
                });
            }

            foreach (var httpspackage in httpspackages)
            {
                var account = await _accountRepository.SelectAsync(httpspackage.AccountId);
                dashBoardCards.Add(new DashBoardCardVO()
                {
                    Title = $"{account?.Name} HTTPS流量包详情",
                    Text = $"当前流量包剩余：{httpspackage?.Size?.ToString("N0") ?? "0"} 次",
                    SubText = $"在跑流量包数量：{httpspackage?.EnableCount ?? 0} 个, 最后检测时间：{httpspackage?.LastUpdateTime}",
                });
            }

            return new ResponseResult<List<DashBoardCardVO>>()
            {
                Code = ResponseCode.Success,
                Data = dashBoardCards
            };
        }
    }
}
