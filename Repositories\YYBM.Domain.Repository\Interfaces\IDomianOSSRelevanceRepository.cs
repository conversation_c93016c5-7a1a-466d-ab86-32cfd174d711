//----------DomianOSSRelevance开始----------

using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Domain.IRepository
{
    /// <summary>
    /// IDomianOSSRelevanceRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-19 16:24:28 
    /// </summary>	
    public interface IDomianOSSRelevanceRepository : IBaseRepository<DomianOSSRelevanceModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<DomianOSSRelevanceModel> SelectPage(DomianOSSRelevanceParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertDomianOSSRelevance(DomianOSSRelevanceModel domianOSSRelevance);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateDomianOSSRelevance(DomianOSSRelevanceModel domianOSSRelevance);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteDomianOSSRelevance(int Id);
    }
}

//----------DomianOSSRelevance结束----------  
