using ZProjectBase.Mvc;

namespace YYBM.Entity.ModelParams
{
    ///<summary>
    ///ProjectConfigParams
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-15 18:12:09 
    ///</summary>
    public partial class ProjectConfigParams : AdminRequestBase
    {

        /// <summary>
        /// Id
        /// </summary>
        public int? Id { get; set; }

        /// <summary>
        /// ParentId
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// Symbol
        /// </summary>
        public string? Symbol { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

    }
}

