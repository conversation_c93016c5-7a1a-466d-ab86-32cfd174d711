//----------OSSBucket开始----------

using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    /// <summary>
    /// OSSBucketService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 19:15:43 
    /// </summary>	
    public class OSSBucketService : AdminServiceBase<OSSBucketModel>, IOSSBucketService
    {

        private readonly IOSSBucketRepository _oSSBucketRepository;

        public OSSBucketService(IHttpContextAccessor httpContextAccessor, IOSSBucketRepository oSSBucketRepository) : base(httpContextAccessor)
        {
            _oSSBucketRepository = oSSBucketRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<OSSBucketVO> GetTableList(OSSBucketParams searchParams)
        {
            var pageResult = _oSSBucketRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            return CreateTableResult<OSSBucketModel, OSSBucketVO>(pageResult);
        }


        /// <summary>
        /// 查询
        /// </summary>
        public OSSBucketVO GetDetail(string Bucket)
        {
            var oSSBucket = _oSSBucketRepository.SelectOSSBucket(Bucket);
            if (oSSBucket == null)
            {
                throw new BusinessException("数据不存在");
            }
            var oSSBucketVO = MapperIns.Map<OSSBucketVO>(oSSBucket);
            return oSSBucketVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddOSSBucket(OSSBucketParams oSSBucketParams)
        {
            var oSSBucket = MapperIns.Map<OSSBucketModel>(oSSBucketParams);
            var result = _oSSBucketRepository.InsertOSSBucket(oSSBucket);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateOSSBucket(OSSBucketParams oSSBucketParams)
        {
            var oSSBucket = MapperIns.Map<OSSBucketModel>(oSSBucketParams);
            var result = _oSSBucketRepository.UpdateOSSBucket(oSSBucket);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteOSSBucket(OSSBucketParams oSSBucketParams)
        {
            CheckParamsUtils.Failure(string.IsNullOrEmpty(oSSBucketParams.Bucket), "Bucket不能为空");

            var result = _oSSBucketRepository.DeleteOSSBucket(oSSBucketParams.Bucket);
            return ResponseResult.Result(result);
        }

    }
}

//----------OSSBucket结束----------
