﻿using Microsoft.EntityFrameworkCore;
using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using YYBM.IRepository;
using ZProjectBase.DB.EF.Repository;
using ZProjectBase.DB.Extend;
using ZProjectBase.Mvc;

namespace YYBM.Repository
{
    /// ActionConfigRepository (EF)
    /// </summary>
    public class ActionConfigRepository : EFBaseRepository<ActionConfigModel>, IActionConfigRepository
    {
        public ActionConfigRepository(DbContextOptions<ActionConfigRepository> options) : base(options) { }

        public DbSet<ActionConfigModel> ActionConfigs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Entity<ActionConfigModel>(entity =>
            {
                entity.ToTable("ActionConfig");

                entity.HasKey(e => e.ActionFlag);
            });
        }

        public PageResult<ActionConfigModel> SelectPage(ActionConfigParams searchParams, PageInfo pageInfo)
        {
            var q = ActionConfigs.AsQueryable();
            if (searchParams.ActionFlag.IsNotNullOrEmpty())
                q = q.Where(x => x.ActionFlag.Contains(searchParams.ActionFlag));
            var total = q.LongCount();
            var skip = pageInfo.page > 1 ? (pageInfo.page - 1) * pageInfo.limit : 0;
            var data = q.Skip(skip).Take(pageInfo.limit).ToList();
            return new PageResult<ActionConfigModel> { Data = data, Count = total };
        }

        public ActionConfigModel SelectActionConfig(string actionFlag)
        {
            return ActionConfigs.FirstOrDefault(x => x.ActionFlag == actionFlag);
        }

        public bool InsertActionConfig(ActionConfigModel actionConfig)
        {
            ActionConfigs.Add(actionConfig);
            return SaveChanges() > 0;
        }

        public bool UpdateActionConfig(ActionConfigModel actionConfig)
        {
            ActionConfigs.Update(actionConfig);
            return SaveChanges() > 0;
        }

        public bool DeleteActionConfig(string actionFlag)
        {
            var entity = SelectActionConfig(actionFlag);
            if (entity == null) return false;
            ActionConfigs.Remove(entity);
            return SaveChanges() > 0;
        }
    }
}
