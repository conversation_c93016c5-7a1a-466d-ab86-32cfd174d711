<template>
  <i :class="icon" v-if="type == 'el'"></i>
  <svg-icon v-else :icon-class="icon"></svg-icon>
</template>

<script>
export default {
  name: 'XIcon',
  props: {
    icon: {
      type: String,
    },
  },
  data() {
    return {}
  },
  computed: {
    type() {
      if (this.icon == undefined) {
        return 'el'
      }
      if (this.icon.startsWith('el')) {
        return 'el'
      } else {
        return 'svg'
      }
    },
  },
  mounted() {
    // console.log('x-icon: ', this.icon)
  },
}
</script>

<style></style>
