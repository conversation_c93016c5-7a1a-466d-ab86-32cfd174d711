<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="桶名称">
          <el-input v-model="queryParams.bucket"></el-input>
        </el-form-item>
        <el-form-item label="项目">
          <x-select
            show-default
            v-model="queryParams.projId"
            url="/options/getProjectOptions"
            customRender
          />
        </el-form-item>
        <el-form-item label="平台">
          <x-select
            show-default
            v-model="queryParams.platform"
            url="/options/getCloudPlatformOptions"
            @change="handlePlatformChange"
          />
        </el-form-item>
        <el-form-item label="账号">
          <x-select show-default v-model="queryParams.accountId" :options="accountOptions" group />
        </el-form-item>
        <el-form-item label="状态">
          <x-select show-default v-model="queryParams.status" :options="bucketStatusOptions" />
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column
          prop="bucket"
          label="桶名称"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column label="项目" align="center" min-width="200">
          <template v-slot="{ row }"> {{ row.projName }}({{ row.projId }}) </template>
        </el-table-column>
        <el-table-column label="平台" align="center" min-width="100">
          <template v-slot="{ row }">
            <platform-icon :platform="row.platform"></platform-icon>
          </template>
        </el-table-column>
        <el-table-column label="账号" align="center" min-width="200">
          <template v-slot="{ row }"> {{ row.accountName }}({{ row.accountId }}) </template>
        </el-table-column>
        <el-table-column label="OSS配置" align="center" min-width="200">
          <template v-slot="{ row }"> {{ row.oSSConfigName }}({{ row.oSSConfigId }}) </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag v-for="item in bucketStatusOptions" :key="item.value">
              <span v-if="row.status === item.value"> {{ item.label }}</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="location"
          label="Location"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="editTime" label="修改时间" align="center" min-width="180">
          <template v-slot="{ row }">
            <span v-if="row.editTime">{{ row.editTime }}</span>
            <span v-else>{{ row.createOn }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['oSSBucket:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['oSSBucket:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import PlatformIcon from '@/components/Business/ShopPlatformIcon'
import { tableHeightMixin } from '@/mixin'
import { ossBucketApi, optionsApi } from '@/api'
import EditDialog from './edit'

export default {
  components: {
    EditDialog,
    PlatformIcon,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        projId: -1,
        platform: -1,
        accountId: -1,
        status: -1,
      },
      loading: false,
      tableData: {},
      accountOptions: [],
      bucketStatusOptions: [],
    }
  },
  created() {
    this.getBucketStatusOptions()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        projId: -1,
        platform: -1,
        accountId: -1,
        status: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await ossBucketApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },

    async getBucketStatusOptions() {
      this.$xloading.show()
      const res = await optionsApi.getBucketStatusOptions()
      if (res.code === 0) {
        this.bucketStatusOptions = res.data
      }
      this.$xloading.hide()
    },

    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await ossBucketApi.delOSSBucket(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    handlePlatformChange() {
      // 当平台变化时，重新加载账号选项
      this.$xloading.show()
      this.accountOptions = []
      optionsApi
        .getCloudAccountOptions(this.queryParams.platform)
        .then((res) => {
          if (res.code === 0) {
            this.accountOptions = res.data
          }
          this.$xloading.hide()
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },
  },
}
</script>
