using YYBM.Cloud.Entity.Enums;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.Entity.ModelParams
{
    ///<summary>
    ///OSSBucketParams
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-18 19:15:35 
    ///</summary>
    public partial class OSSBucketParams : AdminRequestBase
    {

        /// <summary>
        /// Bucket
        /// </summary>
        public string Bucket { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public int ProjId { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        public int Platform { get; set; }

        /// <summary>
        /// AccountId
        /// </summary>
        public int AccountId { get; set; }

        /// <summary>
        /// OSSConfigId
        /// </summary>
        public string OSSConfigId { get; set; }

        /// <summary>
        /// Status
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// Location
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

    }
}

