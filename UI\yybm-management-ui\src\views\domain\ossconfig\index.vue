<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="配置Id">
          <el-input v-model="queryParams.id"></el-input>
        </el-form-item>
        <el-form-item label="配置名称">
          <el-input v-model="queryParams.name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['domainOSSConfig:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column prop="id" label="Id" align="center" min-width="100"></el-table-column>
        <el-table-column prop="name" label="名称" align="center" min-width="200"></el-table-column>
        <el-table-column
          prop="keepCount"
          label="保持桶个数"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="offlineDeleteTime"
          label="下线删除等待时间"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="bannedDeleteTime"
          label="封禁删除等待时间"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column label="归属分组" align="center" min-width="200">
          <template v-slot="{ row }">
            <span>{{ row.domainGroupName }}({{ row.domainGroupId }})</span>
          </template>
        </el-table-column>
        <el-table-column prop="editTime" label="修改时间" align="center" min-width="180">
          <template v-slot="{ row }">
            <span v-if="row.editTime">{{ row.editTime }}</span>
            <span v-else>{{ row.createOn }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['domainOSSConfig:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['domainOSSConfig:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { domainOSSConfigApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await domainOSSConfigApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await domainOSSConfigApi.delDomainOSSConfig(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
