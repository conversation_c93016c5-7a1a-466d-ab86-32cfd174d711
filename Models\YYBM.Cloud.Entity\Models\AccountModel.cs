using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Cloud.Entity.Models
{
    ///<summary>
    ///Account
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-12 14:57:19 
    ///</summary>
    [Table("Account")]
    public partial class AccountModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(true)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// 秘钥Id
        /// </summary>
        [Required]
        public string SecretId { get; set; }

        /// <summary>
        /// 秘钥
        /// </summary>
        [Required]
        public string SecretKey { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        [Required]
        public int Platform { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// CreateTime
        /// </summary>
        [Required]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

        /// <summary>
        /// Bucket总数
        /// </summary>
        public int BucketCount { get; set; }

        /// <summary>
        /// Bucket限制
        /// </summary>
        public int BucketLimit { get; set; }
    }
}

