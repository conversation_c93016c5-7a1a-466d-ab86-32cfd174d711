﻿using ZProjectBase.Mvc;

namespace YYBM.Entity.ModelParams
{
    ///<summary>
    ///ActionConfigParams
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-07-18 15:31:35 
    ///</summary>
    public partial class ActionConfigParams : AdminRequestBase
    {

        /// <summary>
        /// ActionFlag
        /// </summary>
        public string ActionFlag { get; set; }

        /// <summary>
        /// Title
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 配置类型 (string, int, bool, json等)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 操作字符串，使用特定格式来分隔数据
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Enable
        /// </summary>
        public bool Enable { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

    }
}
