﻿using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;

namespace YYBM.Cloud.Service.ObjectStorage.Providers.ECloud
{
    public class ECloudAccountProvider : AccountProvider
    {
        private readonly IAccountRepository _accountRepository;

        public ECloudAccountProvider(IAccountRepository accountRepository) : base(accountRepository)
        {
            _accountRepository = accountRepository;
        }

        public AccountVO GetAccount(int? id)
        {
            if (id.HasValue)
            {
                return GetAccount(id.Value);
            }
            else
            {
                return GetRamdonAccount(CloudPlatform.ECloud);
            }
        }
    }
}
