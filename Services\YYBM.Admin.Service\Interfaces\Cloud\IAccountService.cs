//----------Account开始----------

using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// AccountService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-12 14:57:27 
    /// </summary>	
    public interface IAccountService : IBaseService<AccountModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<AccountVO> GetTableList(AccountParams searchParams);

        /// <summary>
        /// 详情
        /// </summary>
        AccountVO GetDetail(int Id);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddAccount(AccountParams accountParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateAccount(AccountParams accountParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteAccount(AccountParams accountParams);

    }
}

//----------Account结束----------
