import Vue from 'vue'
import { login, logout, getInfo } from '@/api/system/login'
import { yyoauth } from '@/api/system/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'

const getDefaultState = () => {
  return {
    token: '',
    userId: '',
    name: '',
    avatar: '',
    info: {},
    permissions: [], // 权限字符串
  }
}

const state = getDefaultState()

const mutations = {
  RESET_STATE: (state) => {
    Object.assign(state, getDefaultState())
  },
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_UserId: (state, userId) => {
    state.userId = userId
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_INFO: (state, info) => {
    state.info = info
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  },
}

const actions = {
  // user login
  Login({ commit }, userInfo) {
    const { username, password } = userInfo
    return new Promise((resolve, reject) => {
      login({ username: username.trim(), password: password })
        .then((response) => {
          if (response.code == 0) {
            const { data } = response
            commit('SET_TOKEN', data.token)
            setToken(data.token)
            resolve()
          } else {
            reject(new Error(response.msg))
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  OAuthLogin({ commit }, data) {
    return new Promise((resolve, reject) => {
      yyoauth(data)
        .then((response) => {
          if (response.code == 0) {
            const { data } = response
            commit('SET_TOKEN', data.token)
            setToken(data.token)
            resolve(data)
          } else {
            reject(new Error(response.msg))
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // get user info
  GetInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo()
        .then((response) => {
          const { data } = response

          if (response.code != 0) {
            reject(response.msg || '身份验证失败，请重新登录')
          }

          const { nickName, avatar, userId } = data

          commit('SET_INFO', data)
          commit('SET_PERMISSIONS', data.permissions)
          commit('SET_NAME', nickName)
          commit('SET_AVATAR', avatar)
          commit('SET_UserId',userId)
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // user logout
  Logout({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout()
        .then(() => {
          removeToken() // must remove  token  first
          resetRouter()
          commit('RESET_STATE')
          resolve()
        })
        .catch((error) => {
          removeToken() // must remove  token  first
          resetRouter()
          commit('RESET_STATE')
          resolve()
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      removeToken() // must remove  token  first
      commit('RESET_STATE')
      resolve()
    })
  },

  // 切换平台
  // SwitchMinaProject ({
  //   commit
  // }, minaProject) {
  //   commit('SET_MINA_PROJECT', minaProject)
  //   const mpArr = minaProject.split('-')
  //   const minaType = parseInt(mpArr[0])
  //   const projectType = parseInt(mpArr[1])
  //   // console.log(mpArr)
  //   commit('SET_MINA_TYPE', minaType)
  //   commit('SET_PROJECT_TYPE', projectType)
  //   Vue.ls.set('MINA_PROJECT', minaProject)
  //   Vue.ls.set('MINA_TYPE', minaType)
  //   Vue.ls.set('PROJECT_TYPE', projectType)
  // }
}

export default {
  // namespaced: true,
  state,
  mutations,
  actions,
}
