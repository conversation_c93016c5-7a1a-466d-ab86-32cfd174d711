//----------OSSConfig开始----------   

using Microsoft.Extensions.Configuration;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.IRepository;
using YYBM.Repository.SqlDb;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.Repository
{
    /// <summary>
    /// OSSConfigRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 16:15:13 
    /// </summary>	
    public class OSSConfigRepository : YYBMCloudRepository<OSSConfigModel>, IOSSConfigRepository
    {
        public OSSConfigRepository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<OSSConfigModel> SelectPage(OSSConfigParams searchParams, PageInfo pageInfo)
        {
            var pageResult = new PageResult<OSSConfigModel>();
            long total;
            pageResult.Data = conn.GetByPage<OSSConfigModel>(pageInfo.page, pageInfo.limit, out total, param: searchParams);
            pageResult.Count = total;
            return pageResult;
        }

        /// <summary>
        /// 查询
        /// </summary>
        public OSSConfigModel SelectOSSConfig(string Id)
        {
            string whereSql = "where Id = @Id";
            return conn.GetByWhereFirst<OSSConfigModel>(whereSql, new { Id = Id });
        }

        /// <summary>
        /// 新增
        /// </summary>
        public bool InsertOSSConfig(OSSConfigModel oSSConfig)
        {
            var res = conn.Insert(oSSConfig) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool UpdateOSSConfig(OSSConfigModel oSSConfig)
        {
            return conn.UpdateById(oSSConfig) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool DeleteOSSConfig(string Id)
        {
            string whereSql = "where Id = @Id";
            return conn.DeleteByWhere<OSSConfigModel>(whereSql, new { Id = Id }) > 0;
        }
    }
}

//----------OSSConfig结束----------

