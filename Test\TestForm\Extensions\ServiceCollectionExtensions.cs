﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace TestForm.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static void AddCustomServices(this IServiceCollection services, Assembly[] assemblies)
        {
            services.Scan(scan => scan
                .FromAssemblies(assemblies)
                .AddClasses(c => c.Where(t =>
                    t.Name.EndsWith("Service") ||
                    t.Name.EndsWith("Repository")))
                // 只注册接口，不注册具体类型 如果需要直接使用具体类型，可以使用 AsSelfWithInterfaces()
                .AsImplementedInterfaces()
                .WithScopedLifetime());
        }

        public static void AddUtils(this IServiceCollection services, Assembly[] assemblies)
        {
            services.Scan(scan => scan
                .FromAssemblies(assemblies)
                .AddClasses()
                //  同时注册具体类型和所有接口
                .AsSelfWithInterfaces()
                .WithScopedLifetime());
        }
    }
}
