//----------DomainGroup开始----------

using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Domain.IRepository
{
    /// <summary>
    /// IDomainGroupRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:52 
    /// </summary>	
    public interface IDomainGroupRepository : IBaseRepository<DomainGroupModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<DomainGroupModel> SelectPage(DomainGroupParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertDomainGroup(DomainGroupModel domainGroup);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateDomainGroup(DomainGroupModel domainGroup);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteDomainGroup(int Id);
    }
}

//----------DomainGroup结束----------  
