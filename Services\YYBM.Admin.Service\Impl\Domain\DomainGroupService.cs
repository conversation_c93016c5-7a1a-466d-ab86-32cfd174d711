//----------DomainGroup开始----------

using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.Entity.VOs;
using YYBM.Domain.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    /// <summary>
    /// DomainGroupService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:56 
    /// </summary>	
    public class DomainGroupService : AdminServiceBase<DomainGroupModel>, IDomainGroupService
    {

        private readonly IDomainGroupRepository _domainGroupRepository;

        public DomainGroupService(IHttpContextAccessor httpContextAccessor, IDomainGroupRepository domainGroupRepository) : base(httpContextAccessor)
        {
            _domainGroupRepository = domainGroupRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<DomainGroupVO> GetTableList(DomainGroupParams searchParams)
        {
            var pageResult = _domainGroupRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            var tableResult = CreateTableResult<DomainGroupModel, DomainGroupVO>(pageResult);
            return tableResult;
        }


        /// <summary>
        /// 详情
        /// </summary>
        public DomainGroupVO GetDetail(int Id)
        {
            var domainGroup = _domainGroupRepository.Select(Id);
            if (domainGroup == null)
            {
                throw new BusinessException("数据不存在");
            }
            var domainGroupVO = MapperIns.Map<DomainGroupVO>(domainGroup);
            return domainGroupVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddDomainGroup(DomainGroupParams domainGroupParams)
        {
            var domainGroup = MapperIns.Map<DomainGroupModel>(domainGroupParams);
            domainGroup.CreateOn = DateTime.Now;
            var result = _domainGroupRepository.InsertDomainGroup(domainGroup);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateDomainGroup(DomainGroupParams domainGroupParams)
        {
            var domainGroup = MapperIns.Map<DomainGroupModel>(domainGroupParams);
            domainGroup.EditTime = DateTime.Now;
            var result = _domainGroupRepository.UpdateDomainGroup(domainGroup);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteDomainGroup(DomainGroupParams domainGroupParams)
        {
            CheckParamsUtils.Failure(domainGroupParams.Id == 0, "Id不能为空");

            var result = _domainGroupRepository.DeleteDomainGroup(domainGroupParams.Id);
            return ResponseResult.Result(result);
        }

    }
}

//----------DomainGroup结束----------
