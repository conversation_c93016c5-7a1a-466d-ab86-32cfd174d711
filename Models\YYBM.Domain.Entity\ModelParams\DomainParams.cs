using ZProjectBase.Mvc;

namespace YYBM.Domain.Entity.ModelParams
{
    ///<summary>
    ///DomainParams
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-15 19:38:49 
    ///</summary>
    public partial class DomainParams : AdminRequestBase
    {

        /// <summary>
        /// Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public int ProjId { get; set; }

        /// <summary>
        /// 分组Id
        /// </summary>
        public int GroupId { get; set; }

        /// <summary>
        /// Domain
        /// </summary>
        public string Domain { get; set; }

        /// <summary>
        /// 权重
        /// </summary>
        public int Weight { get; set; }

        /// <summary>
        /// 1：上线，0：下线，-1：禁用
        /// </summary>
        public int? State { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

        /// <summary>
        /// 是否随机域名
        /// </summary>
        public bool? IsRandomSLD { get; set; }

        /// <summary>
        /// 是否OSS域名
        /// </summary>
        public bool IsOSS { get; set; }
    }
}

