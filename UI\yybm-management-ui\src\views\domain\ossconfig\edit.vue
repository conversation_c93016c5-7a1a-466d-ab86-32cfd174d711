<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
    width="720px"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium" class="domain-oss-form">
      <el-row :gutter="16">
        <!-- 基本信息 -->
        <el-col :span="24">
          <div class="form-section-title">
            <i class="el-icon-setting"></i>
            基本配置
          </div>
        </el-col>

        <el-col :span="24">
          <el-form-item label="配置ID" prop="id">
            <el-input
              :disabled="true"
              v-model="form.id"
              placeholder="系统自动生成唯一ID"
              class="id-input"
            >
              <template slot="prepend">
                <i class="el-icon-key"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="配置名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入配置名称，用于标识该域名OSS配置"
              clearable
              maxlength="100"
              show-word-limit
            >
              <template slot="prepend">
                <i class="el-icon-edit-outline"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="归属分组" prop="domainGroupId">
            <x-select
              v-model="form.domainGroupId"
              url="/options/getDomainGroupOptions"
              placeholder="选择域名分组"
              style="width: 100%"
            />
            <div class="form-tip">
              <i class="el-icon-info"></i>
              选择该配置所属的域名分组，用于统一管理
            </div>
          </el-form-item>
        </el-col>

        <!-- OSS配置 -->
        <el-col :span="24">
          <div class="form-section-title">
            <i class="el-icon-s-data"></i>
            OSS存储配置
          </div>
        </el-col>

        <el-col :span="24">
          <el-form-item label="保持桶个数" prop="keepCount">
            <el-input-number
              v-model="form.keepCount"
              placeholder="OSS桶保持个数"
              :min="1"
              :max="9999"
              controls-position="right"
              style="width: 100%"
            />
            <div class="form-tip">
              <i class="el-icon-info"></i>
              设置系统同时保持的OSS存储桶数量，建议根据业务需求设置
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="下线等待时间" prop="offlineDeleteTime">
            <el-input-number
              v-model="form.offlineDeleteTime"
              placeholder="分钟"
              :min="0"
              :max="43200"
              controls-position="right"
              style="width: 100%"
            />
            <div class="form-tip-inline">
              <i class="el-icon-time"></i>
              下线后等待删除的时间（分钟）
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="封禁等待时间" prop="bannedDeleteTime">
            <el-input-number
              v-model="form.bannedDeleteTime"
              placeholder="分钟"
              :min="0"
              :max="43200"
              controls-position="right"
              style="width: 100%"
            />
            <div class="form-tip-inline">
              <i class="el-icon-warning"></i>
              封禁后等待删除的时间（分钟）
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <div class="time-config-note">
            <i class="el-icon-info"></i>
            <span>时间配置说明：设置为0表示立即删除，建议设置适当的等待时间以防误操作</span>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { domainOSSConfigApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await domainOSSConfigApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await domainOSSConfigApi.editDomainOSSConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await domainOSSConfigApi.addDomainOSSConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.domain-oss-form {
  padding: 0 8px;
}

.form-section-title {
  display: flex;
  align-items: center;
  padding: 12px 0 16px 0;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #f0f0f0;
}

.form-section-title i {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.form-tip {
  display: flex;
  align-items: flex-start;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-left: 3px solid #409eff;
  border-radius: 4px;
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

.form-tip i {
  margin-right: 6px;
  margin-top: 1px;
  color: #409eff;
  font-size: 14px;
  flex-shrink: 0;
}

.form-tip-inline {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.form-tip-inline i {
  margin-right: 4px;
  font-size: 13px;
}

.time-config-note {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
  border: 1px solid #d1ecf1;
  border-radius: 6px;
  font-size: 13px;
  color: #0c5460;
  margin-top: 8px;
}

.time-config-note i {
  margin-right: 8px;
  margin-top: 1px;
  color: #17a2b8;
  font-size: 14px;
  flex-shrink: 0;
}

.id-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

/* 输入框前置图标样式 */
.el-input-group__prepend {
  background: #fafafa;
  border-color: #dcdfe6;
  color: #909399;
}

/* 表单项间距优化 */
.domain-oss-form .el-form-item {
  margin-bottom: 22px;
}

/* 输入框样式优化 */
.domain-oss-form .el-input__inner {
  border-radius: 6px;
  border-color: #dcdfe6;
  transition: all 0.3s;
}

.domain-oss-form .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 数字输入框样式优化 */
.domain-oss-form .el-input-number {
  width: 100%;
}

.domain-oss-form .el-input-number .el-input__inner {
  text-align: left;
  padding-right: 50px;
}

.domain-oss-form .el-input-number__increase,
.domain-oss-form .el-input-number__decrease {
  background: #fafafa;
  border-color: #dcdfe6;
}

.domain-oss-form .el-input-number__increase:hover,
.domain-oss-form .el-input-number__decrease:hover {
  background: #409eff;
  color: #fff;
}

/* 选择框样式 */
.domain-oss-form .el-select .el-input__inner {
  cursor: pointer;
}

/* 标签样式优化 */
.domain-oss-form .el-form-item__label {
  font-weight: 500;
  color: #303133;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .form-section-title {
    font-size: 14px;
  }

  .domain-oss-form {
    padding: 0;
  }

  .domain-oss-form .el-form-item__label {
    width: 120px !important;
  }
}
</style>
