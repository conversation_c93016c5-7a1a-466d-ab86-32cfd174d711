//----------OSSBucket开始----------   

using Microsoft.Extensions.Configuration;
using System.Text;
using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.IRepository;
using YYBM.Repository.SqlDb;
using ZProjectBase.DB.Extend;
using ZProjectBase.Mvc;

namespace YYBM.Cloud.Repository
{
    /// <summary>
    /// OSSBucketRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-18 19:15:40 
    /// </summary>	
    public class OSSBucketRepository : YYBMCloudRepository<OSSBucketModel>, IOSSBucketRepository
    {
        public OSSBucketRepository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<OSSBucketModel> SelectPage(OSSBucketParams searchParams, PageInfo pageInfo)
        {
            var whereSql = new StringBuilder();
            whereSql.AppendSqlIf($"Bucket = @Bucket", searchParams.Bucket.IsNotNullOrEmpty());
            whereSql.AppendSqlIf($"ProjId = @ProjId", searchParams.ProjId > 0);
            whereSql.AppendSqlIf($"[Platform] = @Platform", searchParams.Platform > 0);
            whereSql.AppendSqlIf($"AccountId = @AccountId", searchParams.AccountId > 0);
            whereSql.AppendSqlIf($"Status = @Status", searchParams.Status.HasValue);

            var orderBy = "order by CreateOn desc";

            var pageResult = new PageResult<OSSBucketModel>();
            long total;
            pageResult.Data = conn.GetByPage<OSSBucketModel>(pageInfo.page, pageInfo.limit, out total, param: searchParams, where: whereSql.ToWhereSql(), orderBy: orderBy);
            pageResult.Count = total;
            return pageResult;
        }

        /// <summary>
        /// 查询
        /// </summary>
        public OSSBucketModel SelectOSSBucket(string Bucket)
        {
            string whereSql = "where Bucket = @Bucket";
            return conn.GetByWhereFirst<OSSBucketModel>(whereSql, new { Bucket = Bucket });
        }

        /// <summary>
        /// 新增
        /// </summary>
        public bool InsertOSSBucket(OSSBucketModel oSSBucket)
        {
            var res = conn.Insert(oSSBucket) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool UpdateOSSBucket(OSSBucketModel oSSBucket)
        {
            return conn.UpdateById(oSSBucket) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool DeleteOSSBucket(string Bucket)
        {
            string whereSql = "where Bucket = @Bucket";
            return conn.DeleteByWhere<OSSBucketModel>(whereSql, new { Bucket = Bucket }) > 0;
        }

        public Task<IEnumerable<OSSBucketModel>> GetListAsync(OSSBucketParams searchParam)
        {
            var whereSql = new StringBuilder();
            whereSql.AppendSqlIf("and ProjId = @ProjId", searchParam.ProjId > 0);
            whereSql.AppendSqlIf("and Platform = @Platform", searchParam.Platform > 0);
            whereSql.AppendSqlIf("and OSSConfigId = @OSSConfigId", searchParam.OSSConfigId.IsNotNullOrEmpty());
            whereSql.AppendSqlIf("and Status = @Status", searchParam.Status.HasValue);

            return conn.GetByWhereAsync<OSSBucketModel>(whereSql.ToWhereSql(), searchParam);
        }

        public bool UpdateStatus(string bucket, BucketStatus status)
        {
            var whereSql = "where Bucket = @Bucket";
            return conn.UpdateByWhere<OSSBucketModel>(whereSql, "Status", new { Bucket = bucket }) > 0;
        }
    }
}

//----------OSSBucket结束----------

