using System.ComponentModel.DataAnnotations;
using YYBM.Cloud.Entity.Enums;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Cloud.Entity.Models
{
    ///<summary>
    ///OSSBucket
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-18 19:15:34 
    ///</summary>
    [Table("OSSBucket")]
    public partial class OSSBucketModel
    {

        /// <summary>
        /// Bucket
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(false)]
        [Required]
        public string Bucket { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        [Required]
        public int ProjId { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        [Required]
        public int Platform { get; set; }

        /// <summary>
        /// AccountId
        /// </summary>
        [Required]
        public int AccountId { get; set; }

        /// <summary>
        /// OSSConfigId
        /// </summary>
        [Required]
        public string OSSConfigId { get; set; }

        /// <summary>
        /// Status
        /// </summary>
        [Required]
        public BucketStatus Status { get; set; }

        /// <summary>
        /// Location
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        [Required]
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

    }
}

