namespace YYBM.Cloud.Entity.VOs
{
    ///<summary>
    ///AccountVO
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-12 14:57:23 
    ///</summary>
    public partial class AccountVO
    {

        /// <summary>
        /// Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 秘钥Id
        /// </summary>
        public string SecretId { get; set; }

        /// <summary>
        /// 秘钥
        /// </summary>
        public string SecretKey { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        public int Platform { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// CreateTime
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

        /// <summary>
        /// 区域，有些云需要
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 服务器节点
        /// </summary>
        public string Endpoint { get; set; }

        /// <summary>
        /// Bucket总数
        /// </summary>
        public int BucketCount { get; set; }

        /// <summary>
        /// Bucket限制
        /// </summary>
        public int BucketLimit { get; set; }
    }
}

