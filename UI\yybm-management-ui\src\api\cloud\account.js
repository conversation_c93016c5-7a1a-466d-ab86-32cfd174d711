import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/account/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/account/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addAccount(data) {
  return request({
    url: '/account/add',
    method: 'post',
    data: data,
  })
}

export function editAccount(data) {
  return request({
    url: '/account/edit',
    method: 'post',
    data: data,
  })
}

export function delAccount(id) {
  return request({
    url: '/account/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------Account结束----------
