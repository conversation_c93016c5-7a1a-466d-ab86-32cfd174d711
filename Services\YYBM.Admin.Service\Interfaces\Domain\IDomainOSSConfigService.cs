//----------DomainOSSConfig开始----------

using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// DomainOSSConfigService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:55 
    /// </summary>	
    public interface IDomainOSSConfigService : IBaseService<DomainOSSConfigModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<DomainOSSConfigVO> GetTableList(DomainOSSConfigParams searchParams);

        /// <summary>
        /// 详情
        /// </summary>
        DomainOSSConfigVO GetDetail(int Id);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddDomainOSSConfig(DomainOSSConfigParams domainOSSConfigParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateDomainOSSConfig(DomainOSSConfigParams domainOSSConfigParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteDomainOSSConfig(DomainOSSConfigParams domainOSSConfigParams);

    }
}

//----------DomainOSSConfig结束----------
