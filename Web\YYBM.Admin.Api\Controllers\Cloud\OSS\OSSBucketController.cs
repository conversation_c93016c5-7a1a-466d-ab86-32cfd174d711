﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Cloud
{
    [Route("api/[controller]")]
    [ApiController]
    public class OSSBucketController : BaseApiController
    {
        private readonly IOSSBucketService _ossBucketService;

        public OSSBucketController(IOSSBucketService ossBucketService)
        {
            _ossBucketService = ossBucketService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("ossBucket:list")]
        public TableResult<OSSBucketVO> GetList([FromQuery] OSSBucketParams searchParams)
        {
            return _ossBucketService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("ossBucket:detail")]
        public ResponseResult<OSSBucketVO> GetDetail([FromQuery] string id)
        {
            return new ResponseResult<OSSBucketVO>(_ossBucketService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("ossBucket:add")]
        public ResponseResult Add([FromBody] OSSBucketParams ossBucketParams)
        {
            return _ossBucketService.AddOSSBucket(ossBucketParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("ossBucket:edit")]
        public ResponseResult Edit([FromBody] OSSBucketParams ossBucketParams)
        {
            return _ossBucketService.UpdateOSSBucket(ossBucketParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("ossBucket:delete")]
        public ResponseResult Delete([FromBody] OSSBucketParams ossBucketParams)
        {
            return _ossBucketService.DeleteOSSBucket(ossBucketParams);
        }
    }
}
