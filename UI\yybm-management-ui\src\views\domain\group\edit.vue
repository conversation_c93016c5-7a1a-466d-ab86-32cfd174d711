<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
    width="600px"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="分组Id" prop="id">
            <el-input-number
              :disabled="isEdit"
              v-model="form.id"
              placeholder="请输入分组Id"
              controls-position="right"
              style="width: 70%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="分组名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入分组名称" />
          </el-form-item>
        </el-col>
        <el-col :span="22">
          <el-form-item label="分组描述" prop="name">
            <el-input
              type="textarea"
              rows="5"
              v-model="form.description"
              placeholder="请输入分组描述"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { domainGroupApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      isEdit: false,
      form: {},
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
      this.isEdit = false
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.isEdit = true
      this.$xloading.show()
      const res = await domainGroupApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          if (this.isEdit) {
            const res = await domainGroupApi.editDomainGroup(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await domainGroupApi.addDomainGroup(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
