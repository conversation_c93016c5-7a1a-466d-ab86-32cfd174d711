﻿using System.Reflection;

namespace YYBM.Admin.Api.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static void AddCustomServices(this IServiceCollection services, Assembly[] assemblies)
        {
            services.Scan(scan => scan
                .FromAssemblies(assemblies)
                .AddClasses(c => c.Where(t =>
                    t.Name.EndsWith("Service") ||
                    t.Name.EndsWith("Repository")))
                .AsImplementedInterfaces()
                .WithScopedLifetime());
        }

        public static void AddUtils(this IServiceCollection services, Assembly[] assemblies)
        {
            services.Scan(scan => scan
                .FromAssemblies(assemblies)
                .AddClasses(c => c.Where(t =>
                    t.Name.EndsWith("Utils")))
                .AsSelfWithInterfaces()
                .WithScopedLifetime());
        }
    }
}
