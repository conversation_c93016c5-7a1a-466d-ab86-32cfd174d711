//----------DomainOSSConfig开始----------

using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using ZProjectBase.DB.IRepository;
using ZProjectBase.Mvc;

namespace YYBM.Domain.IRepository
{
    /// <summary>
    /// IDomainOSSConfigRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:52 
    /// </summary>	
    public interface IDomainOSSConfigRepository : IBaseRepository<DomainOSSConfigModel>//类名
    {
        /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<DomainOSSConfigModel> SelectPage(DomainOSSConfigParams searchParams, PageInfo pageInfo);

        /// <summary>
        /// 新增
        /// </summary>
        bool InsertDomainOSSConfig(DomainOSSConfigModel domainOSSConfig);

        /// <summary>
        /// 更新
        /// </summary>
        bool UpdateDomainOSSConfig(DomainOSSConfigModel domainOSSConfig);

        /// <summary>
        /// 删除
        /// </summary>
        bool DeleteDomainOSSConfig(int Id);
    }
}

//----------DomainOSSConfig结束----------  
