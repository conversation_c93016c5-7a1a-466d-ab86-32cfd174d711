<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="平台">
          <x-select
            show-default
            v-model="queryParams.platform"
            url="/options/getCloudPlatformOptions"
          ></x-select>
        </el-form-item>

        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['account:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column prop="id" label="账号Id" align="center" min-width="100"></el-table-column>
        <el-table-column label="平台" align="center" min-width="100">
          <template v-slot="{ row }">
            <platform-icon :platform="row.platform"></platform-icon>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="账号" align="center" min-width="200"></el-table-column>
        <el-table-column
          prop="secretId"
          label="秘钥Id"
          align="center"
          min-width="250"
        ></el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column label="桶总数/限制" align="center" min-width="150">
          <template v-slot="{ row }"> {{ row.bucketCount }}/{{ row.bucketLimit }} </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['account:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['account:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import PlatformIcon from '@/components/Business/ShopPlatformIcon'
import { tableHeightMixin } from '@/mixin'
import { accountApi } from '@/api'
import EditDialog from './edit'
export default {
  components: {
    EditDialog,
    PlatformIcon,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        platform: -1,
      },
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {
        platform: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await accountApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await accountApi.delAccount(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
