﻿using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Entity.Models
{
    [Table("ActionConfig")]
    public partial class ActionConfigModel
    {

        /// <summary>
        /// ActionFlag
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(false)]
        [Required]
        public string ActionFlag { get; set; }

        /// <summary>
        /// Title
        /// </summary>
        [Required]
        public string Title { get; set; }

        /// <summary>
        /// 配置类型 (string, int, bool, json等)
        /// </summary>
        [Required]
        public string Type { get; set; }

        /// <summary>
        /// 操作字符串，使用特定格式来分隔数据
        /// </summary>
        [Required]
        public string Value { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Enable
        /// </summary>
        [Required]
        public bool Enable { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        [Required]
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

    }
}
