<template>
  <div class="x-video-upload">
    <template v-if="canChoose">
      <el-radio-group v-model="chooseType" size="mini" @change="onChooseTypeChange">
        <el-radio-button label="upload">上传</el-radio-button>
        <el-radio-button label="input">输入链接</el-radio-button>
      </el-radio-group>
    </template>

    <template v-if="chooseType === 'upload'">
      <!-- 视频上传区域 -->
      <div class="video-upload-section">
        <div class="upload-label">视频文件</div>
        <el-upload
          ref="videoUpload"
          v-bind="$attrs"
          v-on="$listeners"
          :action="uploadUrl"
          :headers="headers"
          :show-file-list="true"
          :file-list="videoFileList"
          :data="{ uploadType: videoUploadType, uploadTempId, uploadTemp }"
          :limit="1"
          :accept="accept"
          :on-success="handleVideoUploadSuccess"
          :on-exceed="handleVideoFileExceed"
          :on-remove="handleVideoFileRemove"
          :before-upload="beforeVideoUpload"
          class="video-uploader"
        >
          <el-button size="small" type="primary" icon="el-icon-upload"> 选择视频文件 </el-button>
          <div slot="tip" class="el-upload__tip">
            支持MP4、AVI、MOV格式，大小不超过{{ maxSize }}MB
          </div>
        </el-upload>

        <!-- 视频预览 -->
        <div v-if="videoUrl" class="video-preview">
          <video :src="videoUrl" :poster="coverUrl" controls class="preview-video">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>

      <!-- 封面图片上传区域 -->
      <div v-if="showCover" class="cover-upload-section">
        <div class="upload-label">封面图片</div>
        <el-upload
          ref="coverUpload"
          :action="uploadUrl"
          :headers="headers"
          list-type="picture-card"
          :show-file-list="true"
          :file-list="coverFileList"
          :data="{ uploadType: coverUploadType, uploadTempId, uploadTemp }"
          :limit="1"
          :accept="'image/*'"
          :on-preview="handleCoverPreview"
          :on-success="handleCoverUploadSuccess"
          :on-exceed="handleCoverFileExceed"
          :on-remove="handleCoverFileRemove"
          :before-upload="beforeCoverUpload"
          class="cover-uploader"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <div class="el-upload__tip">建议尺寸：16:9，支持jpg、png格式，大小不超过5MB</div>

        <!-- 封面预览弹窗 -->
        <el-dialog :visible.sync="previewDialogVisible" :append-to-body="true">
          <img width="100%" :src="previewDialogImageUrl" alt="" />
        </el-dialog>
      </div>
    </template>

    <template v-else>
      <!-- 输入链接模式 -->
      <div class="input-section">
        <div class="input-item">
          <div class="input-label">视频链接</div>
          <el-input
            v-model="inputVideoUrl"
            placeholder="请输入视频链接"
            @change="onInputVideoUrlChange"
          >
            <div slot="append">
              <el-button icon="el-icon-refresh-right" @click="loadInputVideo">加载视频</el-button>
            </div>
          </el-input>
        </div>

        <div class="input-item">
          <div class="input-label">封面链接</div>
          <el-input
            v-model="inputCoverUrl"
            placeholder="请输入封面图片链接"
            @change="onInputCoverUrlChange"
          >
            <div slot="append">
              <el-button icon="el-icon-refresh-right" @click="loadInputCover">加载图片</el-button>
            </div>
          </el-input>
        </div>

        <!-- 预览区域 -->
        <div v-if="showInputPreview" class="input-preview">
          <video
            v-if="inputVideoUrl"
            :src="inputVideoUrl"
            :poster="inputCoverUrl"
            controls
            class="preview-video"
            @loadeddata="onInputVideoLoad"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { fileApi } from '@/api'

export default {
  name: 'XVideoUpload',
  inheritAttrs: false,
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    uploadUrl: {
      type: String,
      default: fileApi.uploadUrl,
    },
    videoUploadType: {
      type: String,
      required: true,
    },
    coverUploadType: {
      type: String,
      required: true,
    },
    uploadTempId: {
      type: String,
    },
    uploadTemp: {
      type: String,
    },
    src: {
      type: Object,
      default: () => ({}),
    },
    canChoose: {
      type: Boolean,
      default: false,
    },
    showCover: {
      type: Boolean,
      default: false,
    },
    bindUrl: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: 'video/*',
    },
    maxSize: {
      type: Number,
      default: 50,
    },
  },
  data() {
    return {
      headers: {},
      videoUrl: '',
      coverUrl: '',
      videoFileList: [],
      coverFileList: [],
      previewDialogVisible: false,
      previewDialogImageUrl: '',
      chooseType: 'upload',
      inputVideoUrl: '',
      inputCoverUrl: '',
      showInputPreview: false,
    }
  },
  watch: {
    src: {
      handler(newVal) {
        if (newVal && (newVal.videoUrl || newVal.coverUrl)) {
          if (this.canChoose) {
            this.judgeTypeByValue(this.value, newVal)
          } else {
            this.setFileList(newVal)
          }
        } else {
          this.videoFileList = []
          this.coverFileList = []
        }
      },
      immediate: true,
    },
    value: {
      handler(newVal) {
        if (!newVal || (!newVal.videoUrl && !newVal.coverUrl)) {
          this.videoFileList = []
          this.coverFileList = []
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.headers = {
      'Access-Token': getToken(),
    }
    if (this.src && (this.src.videoUrl || this.src.coverUrl)) {
      if (this.canChoose) {
        this.judgeTypeByValue(this.value, this.src)
      } else {
        this.setFileList(this.src)
      }
    }
  },
  beforeDestroy() {
    this.videoFileList = []
    this.coverFileList = []
    this.inputVideoUrl = ''
    this.inputCoverUrl = ''
    this.showInputPreview = false
  },
  methods: {
    // 视频上传相关方法
    beforeVideoUpload(file) {
      const isVideo = file.type.startsWith('video/')
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize

      if (!isVideo) {
        this.$message.error('只能上传视频文件！')
        return false
      }
      if (!isLtMaxSize) {
        this.$message.error(`视频大小不能超过 ${this.maxSize}MB！`)
        return false
      }
      return true
    },

    // eslint-disable-next-line no-unused-vars
    handleVideoUploadSuccess(res, file, fileList) {
      if (res.code === 0) {
        const { path, url } = res.data
        this.videoUrl = url

        const newValue = {
          ...this.value,
          videoUrl: this.bindUrl ? url : path,
          videoPath: path,
        }

        this.$emit('input', newValue)
        this.$emit('video-success', res)
      } else {
        this.$emit('error', res)
        this.videoFileList.splice(fileList.length - 1, 1)
        this.$message.error(res.msg)
      }
    },

    handleVideoFileExceed(files, fileList) {
      if (fileList.length > 0) {
        this.$set(fileList[0], 'raw', files[0])
        this.$set(fileList[0], 'name', files[0].name)
        this.$refs.videoUpload.clearFiles()
        this.$refs.videoUpload.handleStart(files[0])
        this.$refs.videoUpload.submit()
      }
    },

    handleVideoFileRemove(file, fileList) {
      this.videoFileList = fileList
      this.videoUrl = ''

      const newValue = {
        ...this.value,
        videoUrl: '',
        videoPath: '',
      }

      this.$emit('input', newValue)
    },

    // 封面上传相关方法
    beforeCoverUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isImage) {
        this.$message.error('只能上传图片文件！')
        return false
      }
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB！')
        return false
      }
      return true
    },

    handleCoverPreview(file) {
      this.previewDialogImageUrl = file.url
      this.previewDialogVisible = true
    },

    // eslint-disable-next-line no-unused-vars
    handleCoverUploadSuccess(res, file, fileList) {
      if (res.code === 0) {
        const { path, url } = res.data
        this.coverUrl = url

        const newValue = {
          ...this.value,
          coverUrl: this.bindUrl ? url : path,
          coverPath: path,
        }

        this.$emit('input', newValue)
        this.$emit('cover-success', res)
      } else {
        this.$emit('error', res)
        this.coverFileList.splice(fileList.length - 1, 1)
        this.$message.error(res.msg)
      }
    },

    handleCoverFileExceed(files, fileList) {
      if (fileList.length > 0) {
        this.$set(fileList[0], 'raw', files[0])
        this.$set(fileList[0], 'name', files[0].name)
        this.$refs.coverUpload.clearFiles()
        this.$refs.coverUpload.handleStart(files[0])
        this.$refs.coverUpload.submit()
      }
    },

    handleCoverFileRemove(file, fileList) {
      this.coverFileList = fileList
      this.coverUrl = ''

      const newValue = {
        ...this.value,
        coverUrl: '',
        coverPath: '',
      }

      this.$emit('input', newValue)
    },

    // 输入链接相关方法
    onChooseTypeChange() {
      // 切换类型时清空数据
      /*this.showInputPreview = false
      this.inputVideoUrl = ''
      this.inputCoverUrl = ''
      this.$emit('input', {})*/
    },

    onInputVideoUrlChange() {
      const newValue = {
        ...this.value,
        videoUrl: this.inputVideoUrl,
      }
      this.$emit('input', newValue)
    },

    onInputCoverUrlChange() {
      const newValue = {
        ...this.value,
        coverUrl: this.inputCoverUrl,
      }
      this.$emit('input', newValue)
    },

    loadInputVideo() {
      if (!this.inputVideoUrl) {
        this.$message.error('请输入视频链接')
        return
      }
      this.showInputPreview = true
    },

    loadInputCover() {
      if (!this.inputCoverUrl) {
        this.$message.error('请输入封面链接')
        return
      }
      this.showInputPreview = true
    },

    onInputVideoLoad() {
      this.$emit('video-success', this.inputVideoUrl)
    },

    onInputCoverLoad() {
      this.$emit('cover-success', this.inputCoverUrl)
    },

    // 工具方法
    judgeTypeByValue(value, src) {
      if (!value) {
        return
      }

      const hasHttpUrl =
        (value.videoUrl && value.videoUrl.startsWith('http')) ||
        (value.coverUrl && value.coverUrl.startsWith('http'))

      if (hasHttpUrl) {
        this.chooseType = 'input'
        this.inputVideoUrl = src.videoUrl || ''
        this.inputCoverUrl = src.coverUrl || ''
        this.showInputPreview = true
      } else {
        this.chooseType = 'upload'
        this.setFileList(src)
      }
    },

    setFileList(src) {
      if (src) {
        // 设置视频文件列表
        if (src.videoUrl) {
          this.videoFileList = [{ url: src.videoUrl, name: '视频文件' }]
          this.videoUrl = src.videoUrl
        } else {
          this.videoFileList = []
        }

        // 设置封面文件列表
        if (src.coverUrl) {
          this.coverFileList = [{ url: src.coverUrl, name: '封面图片' }]
          this.coverUrl = src.coverUrl
        } else {
          this.coverFileList = []
        }
      } else {
        this.videoFileList = []
        this.coverFileList = []
      }
    },
  },
}
</script>

<style scoped>
.x-video-upload {
  width: 100%;
}

.video-upload-section,
.cover-upload-section {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafbfc;
}

.upload-label {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
}

.video-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  padding: 20px;
  text-align: center;
  background: white;
}

.video-uploader .el-upload:hover {
  border-color: #409eff;
}

.cover-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.cover-uploader .el-upload:hover {
  border-color: #409eff;
}

.preview-video {
  width: 100%;
  max-width: 600px;
  max-height: 400px;
  margin-top: 10px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.input-section {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafbfc;
}

.input-item {
  margin-bottom: 16px;
}

.input-label {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.input-preview {
  margin-top: 16px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: white;
}

.preview-cover {
  width: 200px;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
  margin-top: 10px;
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}
</style>
