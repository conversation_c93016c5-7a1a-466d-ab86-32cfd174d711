<template>
  <div>
    <el-button v-if="status" type="primary" :size="size" @click="changeStatus(false)"
      >配置动作</el-button
    >
    <el-button v-else type="info" :size="size" @click="changeStatus(true)">取消配置动作</el-button>
  </div>
</template>

<script>
export default {
  props: {
    size: {
      type: String,
      default: 'medium',
    },
  },
  data() {
    return {
      status: true,
    }
  },
  methods: {
    changeStatus(status) {
      this.status = status
      this.$emit('change', status)
    },
  },
}
</script>

<style></style>
