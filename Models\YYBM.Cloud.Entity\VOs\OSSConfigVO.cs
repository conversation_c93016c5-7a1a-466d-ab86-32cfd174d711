namespace YYBM.Cloud.Entity.VOs
{
    ///<summary>
    ///OSSConfigVO
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-18 16:15:11 
    ///</summary>
    public partial class OSSConfigVO
    {

        /// <summary>
        /// Id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 项目Id
        /// </summary>
        public int ProjId { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ProjName { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        public int Platform { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 文件路径，多个使用换行分隔
        /// </summary>
        public string FilesPath { get; set; }

        /// <summary>
        /// 静态网站文件路径
        /// </summary>
        public string WebsiteFilePath { get; set; }

        /// <summary>
        /// 区域与服务器节点设置
        /// </summary>
        public string RegionEndpointsConfig { get; set; }

        /// <summary>
        /// 账号、权重设置
        /// </summary>
        public string AccountsWeightConfig { get; set; }

        /// <summary>
        /// 域名保持设置Id
        /// </summary>
        public int DomainOssConfigId { get; set; }

        public object DomainOssConfig { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enable { get; set; }
    }
}

