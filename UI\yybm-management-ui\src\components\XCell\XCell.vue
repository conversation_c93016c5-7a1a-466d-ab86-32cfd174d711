<template>
  <div class="cell" v-bind:class="dividerClass">
    <div class="cell-label" v-bind:style="cellLabelStyle">
      <slot name="label">{{ label }}</slot>
    </div>
    <div class="cell-content" v-bind:style="cellContentStyle">
      <slot>{{ content }}</slot>
    </div>
    <div class="cell-suffix" v-if="$slots.suffix">
      <slot name="suffix"></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    labelWidth: {
      type: String,
    },
    label: {
      type: String,
    },
    content: {
      type: [String, Number],
    },
    divider: {
      type: Boolean,
      default: false,
    },
  },
  inject: ['cellGroup'],
  computed: {
    dividerClass() {
      return {
        'cell-bottom-border': this.divider,
      }
    },
    cellLabelStyle() {
      let labelW = this.cellGroup.labelWidth
      if (this.labelWidth) {
        labelW = this.labelWidth
      }
      return {
        width: labelW,
      }
    },
    cellContentStyle() {
      let labelW = this.cellGroup.labelWidth
      if (this.labelWidth) {
        labelW = this.labelWidth
      }
      let w = '5px'
      if (this.$slots.suffix) {
        w = '25px'
      }
      return {
        width: `calc(100% - ${labelW} - ${w})`,
      }
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
$label-width: 100px;
$cell-height: 44px;

.cell-bottom-border {
  border-bottom: 1px solid #e6ebf5;
}

.cell {
  color: #515a6e;
  width: 100%;
  display: inline-block;
  position: relative;
  // vertical-align: middle;
  // height: $cell-height;

  .cell-label {
    display: inline-block;
    width: $label-width;
    height: $cell-height;
    line-height: $cell-height;
  }
  .cell-content {
    display: inline-block;
    // align-items: center;
    // align-self: baseline;
    width: calc(100% - #{$label-width} - 5px);
    // height: $cell-height;
    // line-height: $cell-height;
    word-wrap: break-word;
    word-break: normal;
  }
  .cell-suffix {
    // height: $cell-height;
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -8px;
  }
}
</style>
