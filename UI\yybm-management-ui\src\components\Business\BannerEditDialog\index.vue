<template>
  <x-dialog
    scroll
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    destroy-on-close
    @submit="submitForm"
    @cancel="onClose"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="110px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="图片" prop="imageURL">
            <x-upload
              :upload-type="uploadType"
              :can-choose="true"
              v-model="form.imageURL"
              :src="form.imageURL_Url"
            ></x-upload>
            <span class="extra">上传图片，或填入图片完整链接</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              placeholder=""
              :min="0"
              controls-position="right"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="Uri" prop="uri">
            <el-input v-model="form.uri" placeholder="请输入Uri"></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <el-form-item>
            <toggle-button @change="handleActionEditVisible"></toggle-button>
          </el-form-item>
        </el-col>
        <template v-if="actionVisible">
          <el-col :span="24" >
            <el-divider content-position="left">动作配置</el-divider>
          </el-col>
          <el-col :span="24">
            <action-form-item ref="actionFormItem" label-width="110px"></action-form-item>
          </el-col>
        </template> -->
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import XUpload from '@/components/XUpload'
import { bannerApi, mainConfigApi } from '@/api'
import { UPLOAD_TYPE } from '@/utils/constant'

export default {
  components: {
    XUpload,
  },
  props: {
    type: {
      type: String, // 按钮类型，mainConfig: 首页配置按钮，
    },
  },
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        imageURL: [
          {
            required: true,
            message: '请上传图片或填入图片链接',
            trigger: 'blur',
          },
        ],
        sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
      },
      uploadType: UPLOAD_TYPE.appBanner,
    }
  },

  mounted() {},

  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await bannerApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    onClose() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    // 提交的时候注意，要根据不同的按钮类型给不同的api提交
    submitForm() {
      if (!this.type) {
        console.error('请配置Banner所属的类型（参数type）')
        return
      }
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await this.editByType(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await this.addByType(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
    async addByType(data) {
      if (this.type == 'mainConfig') {
        return mainConfigApi.editMainConfig('banner', { banner: data })
      }
    },
    async editByType(data) {
      if (this.type == 'mainConfig') {
        return mainConfigApi.editMainConfig('banner', { banner: data })
      }
    },
  },
}
</script>

<style></style>
