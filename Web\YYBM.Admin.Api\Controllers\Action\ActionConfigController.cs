﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Entity.ModelParams;
using YYBM.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Action
{
    [Route("api/[controller]")]
    [ApiController]
    public class ActionConfigController : BaseApiController
    {
        private readonly IActionConfigService _actionConfigService;

        public ActionConfigController(IActionConfigService actionConfigService)
        {
            _actionConfigService = actionConfigService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("actionConfig:list")]
        public TableResult<ActionConfigVO> GetList([FromQuery] ActionConfigParams searchParams)
        {
            return _actionConfigService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("actionConfig:detail")]
        public ResponseResult<ActionConfigVO> GetDetail([FromQuery] string id)
        {
            return new ResponseResult<ActionConfigVO>(_actionConfigService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("actionConfig:add")]
        public ResponseResult Add([FromBody] ActionConfigParams ActionConfigParams)
        {
            return _actionConfigService.AddActionConfig(ActionConfigParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("actionConfig:edit")]
        public ResponseResult Edit([FromBody] ActionConfigParams ActionConfigParams)
        {
            return _actionConfigService.UpdateActionConfig(ActionConfigParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("actionConfig:delete")]
        public ResponseResult Delete([FromBody] ActionConfigParams ActionConfigParams)
        {
            return _actionConfigService.DeleteActionConfig(ActionConfigParams);
        }
    }
}
