import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/domain/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/domain/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addDomain(data) {
  return request({
    url: '/domain/add',
    method: 'post',
    data: data,
  })
}

export function editDomain(data) {
  return request({
    url: '/domain/edit',
    method: 'post',
    data: data,
  })
}

export function delDomain(id) {
  return request({
    url: '/domain/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------Domain结束----------
