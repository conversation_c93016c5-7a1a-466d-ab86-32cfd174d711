<template>
  <div>
    <template v-if="canChoose">
      <el-radio-group v-model="chooseType" size="mini" @change="onChooseTypeChnage">
        <el-radio-button label="upload">上传</el-radio-button>
        <el-radio-button label="input">输入链接</el-radio-button>
      </el-radio-group>
    </template>
    <template v-if="chooseType == 'upload'">
      <el-upload
        ref="upload"
        v-bind="$attrs"
        v-on="$listeners"
        :action="uploadUrl"
        :headers="headers"
        list-type="picture-card"
        :show-file-list="true"
        :file-list="fileList"
        :data="{ uploadType, uploadTempId, uploadTemp }"
        :limit="limit"
        :on-preview="handlePictureCardPreview"
        :on-success="handleUploadSuccess"
        :on-exceed="handleFileExceed"
        :on-remove="handleFileRemove"
      >
        <i class="el-icon-plus"></i>
      </el-upload>
      <el-dialog :visible.sync="previewDialogVisible" :append-to-body="true">
        <img width="100%" :src="previewDialogImageUrl" alt="" />
      </el-dialog>
    </template>
    <template v-else>
      <el-input v-model="inputUrl" placeholder="请输入链接" @change="onInputUrlChange">
        <div slot="append">
          <el-button icon="el-icon-refresh-right" @click="loadInputImage">加载图片</el-button>
        </div>
      </el-input>
      <!-- 预览图片 -->
      <el-image
        v-if="showInputImage"
        :src="inputUrl"
        @load="onInputImageLoad"
        style="width: 200px; height: 200px; margin-top: 10px"
      ></el-image>
    </template>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { fileApi } from '@/api'
export default {
  inheritAttrs: false,
  props: {
    value: {
      type: [String, Array],
    },
    uploadUrl: {
      type: String,
      default: fileApi.uploadUrl,
    },
    uploadType: {
      type: String,
    },
    uploadTempId: {
      type: String,
    },
    uploadTemp: {
      type: String,
    },
    limit: {
      type: Number,
      default: 1,
    },
    src: [String, Array],
    canChoose: {
      // 是否允许选择“输入链接”、“上传图片”
      type: Boolean,
      default: false,
    },
    bindUrl: {
      // 是否绑定url
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      headers: {},
      imageUrl: '',
      fileList: [],
      previewDialogVisible: false,
      previewDialogImageUrl: false,
      chooseType: 'upload', // 文件类型，upload：上传，input：输入链接
      inputUrl: undefined,
      showInputImage: false,
    }
  },
  watch: {
    src(newVal) {
      // console.log('src:', newVal)
      if (newVal) {
        if (this.canChoose) {
          this.judgeTypeByValue(this.value, newVal)
        } else {
          this.setFileList(newVal)
        }
      } else {
        this.fileList = []
      }
    },
    value(newVal) {
      // console.log('value: ', newVal)
      if (!newVal) {
        this.fileList = []
      }
      if (Array.isArray(newVal) && newVal.length == 0) {
        this.fileList = []
      }
    },
  },
  mounted() {
    this.headers = {
      'Access-Token': getToken(),
    }
    // console.log('src:', this.src)
    if (this.src) {
      if (this.canChoose) {
        // 判断是上传还是输入的链接
        this.judgeTypeByValue(this.value, this.src)
      } else {
        this.setFileList(this.src)
      }
    }
  },
  beforeDestroy() {
    this.fileList = []
    this.inputUrl = undefined
    this.showInputImage = false
    // console.log('beforeDestroy')
  },
  methods: {
    handlePictureCardPreview(file) {
      // console.log('file:', file)
      this.previewDialogImageUrl = file.url
      this.previewDialogVisible = true
    },
    // eslint-disable-next-line no-unused-vars
    handleUploadSuccess(res, file, fileList) {
      if (res.code == 0) {
        const { path, url } = res.data
        this.imageUrl = url
        // this.value = path
        if (this.limit == 1) {
          if (this.bindUrl) {
            this.$emit('input', url)
          } else {
            this.$emit('input', path)
          }
          this.$emit('success', res)
        } else {
          // console.log(this.value)
          if (this.bindUrl) {
            this.$emit('input', [...this.value, url])
          } else {
            this.$emit('input', [...this.value, path])
          }
          this.$emit('success', res)
        }
      } else {
        this.$emit('error', res)
        this.fileList.splice(fileList.length - 1, 1)
        this.$xMsgError(res.msg)
      }
      // console.log(arguments)
    },

    handleFileExceed(files, fileList) {
      // console.log(files)
      // console.log(fileList)
      // 单文件上传时，超出就覆盖上传
      if (this.limit == 1) {
        this.$set(fileList[0], 'raw', files[0])
        this.$set(fileList[0], 'name', files[0].name)
        this.$refs.upload.clearFiles()
        this.$refs.upload.handleStart(files[0])
        this.$refs.upload.submit()
      }
    },
    handleFileRemove(file, fileList) {
      // console.log('del', fileList)
      this.fileList = fileList
      if (this.limit == 1) {
        this.$emit('input', undefined)
      } else {
        // console.log(file)
        if (file.idx != undefined) {
          this.deleteByIdx(file.idx)
          return
        }
        let path = file.response.data.path
        // console.log(file.response.data.path)
        // console.log(fileList)
        // console.log(this.value)
        const idx = this.value.findIndex((t) => t == path)
        if (idx != -1) {
          this.deleteByIdx(idx)
        }
      }
    },

    deleteByIdx(idx) {
      // console.log('idx:', idx)
      // console.log(this.value)
      let values = this.value
      values.splice(idx, 1)
      this.$emit('input', values)
    },

    onChooseTypeChnage() {
      // 先清空所有值，后面再优化了
      /*this.showInputImage = false
      this.inputUrl = undefined
      this.$emit('input', undefined)*/
    },

    onInputUrlChange() {
      this.$emit('input', this.inputUrl)
    },

    loadInputImage() {
      if (!this.inputUrl) {
        this.$xMsgError('请输入链接')
        return
      }
      // TODO: 校验链接格式
      this.showInputImage = true
    },

    onInputImageLoad() {
      // this.$emit('input', this.inputUrl)
      // this.$emit('success', this.inputUrl)
    },
    judgeTypeByValue(value, src) {
      if (!value) {
        return
      }
      if (!Array.isArray(this.value) && this.value.startsWith('http')) {
        this.chooseType = 'input'
        this.inputUrl = src
        this.showInputImage = true
      } else {
        this.chooseType = 'upload'
        this.setFileList(src)
      }
    },
    setFileList(src) {
      if (src) {
        if (Array.isArray(src)) {
          this.fileList = src.map((t, idx) => {
            return { url: t, idx }
          })
        } else {
          this.fileList = [{ url: src }]
        }
      } else {
        this.fileList = []
      }
    },
  },
}
</script>

<style></style>
