﻿using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.IRepository;
using ZProjectBase.Common.Extend;
using ZProjectBase.Common.Ioc;
using ZProjectBase.Common.Utils;

namespace YYBM.Cloud.Service.ObjectStorage.Mappers
{
    public class AccountsWeightConfigMapper
    {
        private readonly static IAccountRepository _accountRepository = HttpContextServiceLocator.GetService<IAccountRepository>();

        public static List<AccountsWeightConfig> MapToAccountsWeightConfigList(OSSConfigModel oSSConfig)
        {
            var accountsWeightConfig = oSSConfig.AccountsWeightConfig;
            if (accountsWeightConfig.IsNullOrEmpty())
            {
                throw new ArgumentException("AccountsWeightConfig cannot be null or empty.");
            }

            var accountsWeightConfigs = new List<AccountsWeightConfig>();

            var accountWeights = accountsWeightConfig.Splitline();
            foreach (var item in accountWeights)
            {
                if (item.StartsWith("--"))
                    continue;

                var arrary = item.Split('@');
                var accountId = arrary[0].Trim().ToInt();
                var weight = arrary.Length >= 1 ? arrary[1].Trim().ToInt() : 0;
                var account = _accountRepository.Select(accountId);
                if (account == null)
                {
                    throw new ArgumentException($"Account with ID {accountId} does not exist.");
                }

                if (account.BucketCount < account.BucketLimit || account.BucketLimit == 0)
                {
                    accountsWeightConfigs.Add(new AccountsWeightConfig
                    {
                        AccountId = accountId,
                        Weight = weight
                    });
                }
                else
                {
                    throw new ArgumentException($"Account with ID {accountId} has reached its bucket limit.");
                }
            }

            return accountsWeightConfigs;
        }

        public static AccountsWeightConfig GetRandomAccount(OSSConfigModel oSSConfig)
        {
            return RandomUtils.Random<AccountsWeightConfig>(MapToAccountsWeightConfigList(oSSConfig));
        }
    }

    public class AccountsWeightConfig : IWeightedItem
    {
        public int AccountId { get; set; }
        public int Weight { get; set; }
    }
}
