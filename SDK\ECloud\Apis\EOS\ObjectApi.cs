﻿using Amazon.S3;
using Amazon.S3.Model;
using ECloud.Configs;

namespace ECloud.Apis.EOS
{
    public class ObjectApi : EOSBaseApi
    {
        public ObjectApi(string accesskey, string secretkey)
        : base(accesskey, secretkey, DataCenterBaseEndpointConfig.BaseEndpoint)
        {
        }

        public ObjectApi(string accessKeyId, string accessKeySecret, string endpoint)
            : base(accessKeyId, accessKeySecret, endpoint)
        {
        }

        public ListObjectsResponse ListObjects(string bucketName)
        {
            var objects = EOSClient.ListObjectsAsync(bucketName).GetAwaiter().GetResult();

            if (objects.HttpStatusCode == System.Net.HttpStatusCode.OK)
            {
                return objects;
            }
            else
            {
                throw new Exception($"Failed to list objects: {objects.HttpStatusCode}");
            }
        }

        public GetObjectResponse GetObject(string bucketName, string key)
        {
            GetObjectRequest request = new GetObjectRequest
            {
                BucketName = bucketName,
                Key = key
            };
            var response = EOSClient
                .GetObjectAsync(request)
                .GetAwaiter()
                .GetResult();

            if (response.HttpStatusCode != System.Net.HttpStatusCode.OK)
            {
                throw new Exception($"Failed to get object: {response.HttpStatusCode}");
            }
            return response;
        }

        public bool PutObjectFromFile(string bucketName, string key, string filePath)
        {
            PutObjectRequest putObjectRequest;

            using (Stream fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                putObjectRequest = new PutObjectRequest()
                {
                    BucketName = bucketName,
                    Key = key,
                    InputStream = fileStream,
                    CannedACL = S3CannedACL.PublicRead
                };

                var res = EOSClient.PutObjectAsync(putObjectRequest).GetAwaiter().GetResult();
                return res.HttpStatusCode == System.Net.HttpStatusCode.OK;
            }
        }

        public bool DeleteObject(string bucketName, string key)
        {
            DeleteObjectRequest request = new DeleteObjectRequest
            {
                BucketName = bucketName,
                Key = key
            };

            var response = EOSClient.DeleteObjectAsync(request).GetAwaiter().GetResult();
            return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }

        public bool DeleteObjects(string bucketName)
        {
            DeleteObjectsRequest request = new DeleteObjectsRequest()
            {
                BucketName = bucketName
            };

            var response = EOSClient.DeleteObjectsAsync(request).GetAwaiter().GetResult();
            return response.HttpStatusCode == System.Net.HttpStatusCode.OK;
        }
    }
}
