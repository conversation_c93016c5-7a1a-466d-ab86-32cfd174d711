﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Project\YYLeagueSupplier\SDK\ZProjectBase.Admin.Core\ZProjectBase.Admin.Core.csproj" />
    <ProjectReference Include="..\..\Models\YYBM.Cloud.Entity\YYBM.Cloud.Entity.csproj" />
    <ProjectReference Include="..\..\Models\YYBM.Domain.Entity\YYBM.Domain.Entity.csproj" />
    <ProjectReference Include="..\..\Models\YYBM.Entity\YYBM.Entity.csproj" />
    <ProjectReference Include="..\..\Repositories\YYBM.Cloud.Repository\YYBM.Cloud.Repository.csproj" />
    <ProjectReference Include="..\..\Repositories\YYBM.Domain.Repository\YYBM.Domain.Repository.csproj" />
    <ProjectReference Include="..\..\Repositories\YYBM.Repository\YYBM.Repository.csproj" />
    <ProjectReference Include="..\YYBM.Common.Service\YYBM.Common.Service.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Impl\Cloud\" />
    <Folder Include="Impl\Project\" />
    <Folder Include="Impl\Domain\" />
    <Folder Include="Interfaces\Cloud\" />
    <Folder Include="Interfaces\Project\" />
    <Folder Include="Interfaces\Domain\" />
  </ItemGroup>

</Project>
