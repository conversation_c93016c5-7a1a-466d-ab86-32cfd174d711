<template>
  <div class="app-container" v-loading="loading">
    <el-card>
      <div slot="header">清理缓存</div>
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="medium">
          <el-row>
            <el-form-item label="key" prop="key">
              <el-input
                v-model.trim="form.key"
                placeholder="请输入key清理缓存"
                clearable
              ></el-input>
              <span class="extra"
                >注意：不需要输入key的前缀；支持批量删除，当key以*结尾时（如xxx*），将删除所有以xxx开头的Key</span
              >
            </el-form-item>
            <div class="history" v-if="historyKeys.length > 0">
              <span class="text">历史记录：</span>
              <el-tag
                v-for="(item, idx) in historyKeys"
                :key="idx"
                @close="onTagClose(idx)"
                @click="onTagClick(item)"
                class="item"
                size="mini"
                closable
                >{{ item }}</el-tag
              >
            </div>
            <el-form-item label=" ">
              <el-button type="primary" @click="handleSubmit">清理缓存</el-button>
              <!-- <el-button @click="handleReset">重置</el-button> -->
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { redisApi } from '@/api'

export default {
  data() {
    return {
      loading: false,
      form: {},
      rules: {
        // TODO: 字符串校验
        key: [{ required: true, message: '请输入key' }],
      },
      historyKeys: [],
    }
  },
  mounted() {
    this.reload()
  },
  methods: {
    handleSubmit() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          this.$xloading.show()
          const res = await redisApi.deleteByKey(this.form.key)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$xMsgSuccess('删除成功')
            this.saveKeyHistory(this.form.key)
          } else {
            this.$xMsgError(`删除失败！${res.msg}`)
          }
        }
      })
    },
    handleReset() {
      this.form = {}
    },
    reload() {
      this.historyKeys = this.getHistory() || []
    },
    saveKeyHistory(key) {
      if (!key) {
        return
      }
      // 获取历史记录，如果有记录，判断key是否已经存在，放到0位置
      let history = this.getHistory()
      if (history) {
        if (history.includes(key)) {
          const idx = history.findIndex((value) => value == key)
          history.splice(idx, 1)
          history.unshift(key)
          this.setHistory(history)
        } else {
          // 最多保存10条记录
          if (history.length == 10) {
            // 删除最后一个
            history.splice(history.length - 1, 1)
          }
          history.unshift(key)
          this.setHistory(history)
        }
      } else {
        this.setHistory([key])
      }
    },
    setHistory(history) {
      Vue.ls.set('redis_key_history', history)
      this.historyKeys = history
    },
    getHistory() {
      let history = Vue.ls.get('redis_key_history')
      return history
    },
    onTagClose(idx) {
      let history = this.getHistory()
      history.splice(idx, 1)
      this.setHistory(history)
    },
    onTagClick(key) {
      // console.log(key)
      // this.form.key = key
      this.$set(this.form, 'key', key)
    },
  },
}
</script>

<style lang="scss" scoped>
.history {
  margin-bottom: 15px;

  .text {
    color: #c0c4cc;
  }
  .item {
    margin-bottom: 5px;
    margin-right: 5px;
  }
}
</style>
