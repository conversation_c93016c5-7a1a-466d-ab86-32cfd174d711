﻿using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;
using YYBM.Cloud.Service.ObjectStorage.Interfaces;
using ZProjectBase.Common.Utils;

namespace YYBM.Cloud.Service.ObjectStorage.Providers
{
    public class AccountProvider : IAccountProvider
    {
        private readonly IAccountRepository _accountRepository;

        public AccountProvider(IAccountRepository accountRepository)
        {
            _accountRepository = accountRepository;
        }
        public AccountVO GetAccount(int id)
        {
            var account = _accountRepository.Select(id);
            if (account == null)
                throw new Exception($"Account with ID {id} not found.");
            return MapperUtil.MapperIns.Map<AccountVO>(account);
        }

        public AccountVO GetRamdonAccount(CloudPlatform platform)
        {
            var account = _accountRepository
                .GetAll()
                .Where(x => x.Platform == (int)platform)
                .OrderBy(x => Guid.NewGuid())
                .FirstOrDefault();

            if (account == null)
                throw new Exception($"Account not found.");

            return MapperUtil.MapperIns.Map<AccountVO>(account);
        }
    }
}
