<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="项目">
          <x-select
            show-default
            v-model="queryParams.projId"
            url="/options/getProjectOptions"
            customRender
          />
        </el-form-item>
        <el-form-item label="平台">
          <x-select
            show-default
            v-model="queryParams.platform"
            url="/options/getCloudPlatformOptions"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['ossConfig:add']"
          >新增</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <!-- ID 列：截断显示 + 复制 -->
        <el-table-column prop="id" label="ID" align="center" min-width="180">
          <template v-slot="{ row }">
            <div class="id-cell">
              <span class="id-text" :title="row.id">{{ formatId(row.id) }}</span>
              <el-tooltip effect="dark" content="复制ID" placement="top">
                <el-button type="text" size="mini" class="copy-btn" @click="onCopyId(row, $event)"
                  >复制</el-button
                >
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" align="center" min-width="200"></el-table-column>
        <el-table-column label="项目" align="center" min-width="200">
          <template v-slot="{ row }"> {{ row.projName }}({{ row.projId }}) </template>
        </el-table-column>
        <el-table-column label="平台" align="center" min-width="100">
          <template v-slot="{ row }">
            <platform-icon :platform="row.platform"></platform-icon>
          </template>
        </el-table-column>
        <el-table-column
          prop="domainOssConfig.keepCount"
          label="保持桶个数"
          align="center"
          min-width="100"
        ></el-table-column>
        <!-- 下线删除等待时间 -> 下线等待（带表头提示） -->
        <el-table-column prop="domainOssConfig.offlineDeleteTime" align="center" min-width="110">
          <template v-slot:header>
            <span>下线等待</span>
            <el-tooltip effect="dark" content="下线删除等待时间" placement="top">
              <i class="el-icon-question header-tip"></i>
            </el-tooltip>
          </template>
          <template v-slot="{ row }">{{ row.domainOssConfig.offlineDeleteTime }}</template>
        </el-table-column>
        <!-- 封禁删除等待时间 -> 封禁等待（带表头提示） -->
        <el-table-column prop="domainOssConfig.bannedDeleteTime" align="center" min-width="110">
          <template v-slot:header>
            <span>封禁等待</span>
            <el-tooltip effect="dark" content="封禁删除等待时间" placement="top">
              <i class="el-icon-question header-tip"></i>
            </el-tooltip>
          </template>
          <template v-slot="{ row }">{{ row.domainOssConfig.bannedDeleteTime }}</template>
        </el-table-column>
        <el-table-column label="归属分组" align="center" min-width="150">
          <template v-slot="{ row }">
            <span
              >{{ row.domainOssConfig.domainGroupName }}({{
                row.domainOssConfig.domainGroupId
              }})</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="enable" label="状态" align="center" min-width="80">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">启用</el-tag>
            <el-tag type="danger" size="medium" v-else>禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="editTime" label="修改时间" align="center" min-width="180">
          <template v-slot="{ row }">
            <span v-if="row.editTime">{{ row.editTime }}</span>
            <span v-else>{{ row.createOn }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['ossConfig:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['ossConfig:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import PlatformIcon from '@/components/Business/ShopPlatformIcon'
import { tableHeightMixin } from '@/mixin'
import { ossConfigApi } from '@/api'
import EditDialog from './edit'
import handleClipboard from '@/utils/clipboard'
export default {
  components: {
    EditDialog,
    PlatformIcon,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        projId: -1,
        platform: -1,
      },
      loading: false,
      tableData: {},
    }
  },
  methods: {
    // ID 展示优化：前8+…+后6
    formatId(id) {
      if (!id) return ''
      const s = String(id)
      return s.length > 16 ? `${s.slice(0, 8)}…${s.slice(-6)}` : s
    },
    // 复制 ID
    onCopyId(row, event) {
      handleClipboard(row.id, event)
    },
    queryReset() {
      this.queryParams = {
        projId: -1,
        platform: -1,
      }
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await ossConfigApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await ossConfigApi.delOSSConfig(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>

<style scoped>
.id-cell {
  display: inline-flex;
  align-items: center;
}
.id-text {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
}
.copy-btn {
  margin-left: 6px;
  padding: 0;
}
.header-tip {
  margin-left: 4px;
  color: #909399;
  cursor: help;
}
</style>
