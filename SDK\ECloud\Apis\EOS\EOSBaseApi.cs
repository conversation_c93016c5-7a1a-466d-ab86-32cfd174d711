﻿using Amazon.S3;

namespace ECloud.Apis.EOS
{
    public class E<PERSON><PERSON><PERSON><PERSON><PERSON>
    {
        public AmazonS3Client EOSClient { get; set; }

        private AmazonS3Config _EOSClientConfig { get; set; }

        public EOSBaseApi(string accessKeyId, string accessKeySecret, string endpoint)
        {
            _EOSClientConfig = new AmazonS3Config()
            {
                ServiceURL = endpoint,
                //SignatureVersion = "2",
                SignatureMethod = Amazon.Runtime.SigningAlgorithm.HmacSHA1,
            };

            EOSClient = new AmazonS3Client(accessKeyId, accessKeySecret, _EOSClientConfig);
        }
    }
}
