using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Cloud.Entity.Models
{
    ///<summary>
    ///Trafficpackage
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-12 14:57:19 
    ///</summary>
    [Table("Trafficpackage")]
    public partial class TrafficpackageModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(true)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        [Required]
        public int Platform { get; set; }

        /// <summary>
        /// 账号Id
        /// </summary>
        [Required]
        public int AccountId { get; set; }

        /// <summary>
        /// 流量包大小
        /// </summary>
        [Required]
        public decimal GB { get; set; }

        /// <summary>
        /// 已经使用
        /// </summary>
        [Required]
        public decimal GBUsed { get; set; }

        /// <summary>
        /// LastUpdateTime
        /// </summary>
        public DateTime? LastUpdateTime { get; set; }

        /// <summary>
        /// 再用个数
        /// </summary>
        public int EnableCount { get; set; }
    }
}

