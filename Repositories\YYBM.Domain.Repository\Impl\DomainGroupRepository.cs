//----------DomainGroup开始----------   

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Text;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.IRepository;
using YYBM.Domain.Repository.SqlDb;
using ZProjectBase.DB.EF.Repository;
using ZProjectBase.DB.Extend;
using ZProjectBase.Mvc;

namespace YYBM.Domain.Repository
{
    /// <summary>
    /// DomainGroupRepository (EF)
    /// </summary>
    public class DomainGroupRepository : EFBaseRepository<DomainGroupModel>, IDomainGroupRepository
    {
        public DomainGroupRepository(DbContextOptions<DomainGroupRepository> options) : base(options) { }

        public DbSet<DomainGroupModel> DomainGroups { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            modelBuilder.Entity<DomainGroupModel>(entity =>
            {
                entity.ToTable("DomainGroup");

                entity.HasKey(e => e.Id);
            });
        }

        public PageResult<DomainGroupModel> SelectPage(DomainGroupParams searchParams, PageInfo pageInfo)
        {
            var q = DomainGroups.AsQueryable();
            if (searchParams.Id > 0)
            {
                q = q.Where(x => x.Id == searchParams.Id);
            }
            if (searchParams.Name.IsNotNullOrEmpty())
            {
                q = q.Where(x => x.Name.Contains(searchParams.Name));
            }

            q = q.OrderByDescending(x => x.CreateOn);

            var total = q.LongCount();
            var skip = pageInfo.page > 1 ? (pageInfo.page - 1) * pageInfo.limit : 0;
            var data = q.Skip(skip).Take(pageInfo.limit).ToList();
            return new PageResult<DomainGroupModel> { Data = data, Count = total };
        }

        public DomainGroupModel SelectDomainGroup(int id)
        {
            return DomainGroups.FirstOrDefault(x => x.Id == id);
        }

        public bool InsertDomainGroup(DomainGroupModel domainGroup)
        {
            DomainGroups.Add(domainGroup);
            return SaveChanges() > 0;
        }

        public bool UpdateDomainGroup(DomainGroupModel domainGroup)
        {
            DomainGroups.Update(domainGroup);
            return SaveChanges() > 0;
        }

        public bool DeleteDomainGroup(int id)
        {
            var entity = SelectDomainGroup(id);
            if (entity == null) return false;
            DomainGroups.Remove(entity);
            return SaveChanges() > 0;
        }
    }
}

//----------DomainGroup结束----------

