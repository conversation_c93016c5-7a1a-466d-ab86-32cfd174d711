﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Domain
{
    [Route("api/[controller]")]
    [ApiController]
    public class DomainGroupController : BaseApiController
    {
        private readonly IDomainGroupService _domainGroupService;

        public DomainGroupController(IDomainGroupService domainGroupService)
        {
            _domainGroupService = domainGroupService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("domainGroup:list")]
        public TableResult<DomainGroupVO> GetList([FromQuery] DomainGroupParams searchParams)
        {
            return _domainGroupService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("domainGroup:detail")]
        public ResponseResult<DomainGroupVO> GetDetail([FromQuery] int id)
        {
            return new ResponseResult<DomainGroupVO>(_domainGroupService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("domainGroup:add")]
        public ResponseResult Add([FromBody] DomainGroupParams domainParams)
        {
            return _domainGroupService.AddDomainGroup(domainParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("domainGroup:edit")]
        public ResponseResult Edit([FromBody] DomainGroupParams domainParams)
        {
            return _domainGroupService.UpdateDomainGroup(domainParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("domainGroup:delete")]
        public ResponseResult Delete([FromBody] DomainGroupParams domainParams)
        {
            return _domainGroupService.DeleteDomainGroup(domainParams);
        }
    }
}
