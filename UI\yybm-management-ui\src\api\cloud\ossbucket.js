import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/oSSBucket/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/oSSBucket/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addOSSBucket(data) {
  return request({
    url: '/oSSBucket/add',
    method: 'post',
    data: data,
  })
}

export function editOSSBucket(data) {
  return request({
    url: '/oSSBucket/edit',
    method: 'post',
    data: data,
  })
}

export function delOSSBucket(id) {
  return request({
    url: '/oSSBucket/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------OSSBucket结束----------
