<template>
  <el-dialog
    v-bind="$attrs"
    v-on="$listeners"
    :close-on-click-modal="false"
    :width="width"
    top="10vh"
  >
    <div class="el-dialog-div" v-if="scroll">
      <el-scrollbar style="height: 100%;">
        <slot></slot>
      </el-scrollbar>
    </div>
    <slot v-else></slot>

    <span slot="footer" class="dialog-footer">
      <slot name="footer">
        <el-button size="medium" type="primary" @click="handleSubmit">确 定</el-button>
        <el-button size="medium" @click="handleCancel">取 消</el-button>
      </slot>
    </span>
  </el-dialog>
</template>

<script>
export default {
  inheritAttrs: false,
  props: {
    scroll: {
      // 是否固定高度，会出现滚动条
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '800px',
    },
  },
  methods: {
    handleSubmit() {
      this.$emit('submit')
    },
    handleCancel() {
      this.$emit('cancel')
    },
  },
}
</script>

<style></style>
