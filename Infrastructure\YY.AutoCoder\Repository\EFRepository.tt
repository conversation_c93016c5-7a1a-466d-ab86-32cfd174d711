<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude" #>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<#
    var outputPath = Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
    outputPath = Path.Combine(outputPath, OutputDllPath, "Temp", "EFRepository");
    if (!Directory.Exists(outputPath)) Directory.CreateDirectory(outputPath);
#>

//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//     生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#>
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# var tableName=config.TableName; #>
<# if(tableName!=""){ #>
using Microsoft.EntityFrameworkCore;
using ZProjectBase.DB.EF.Repository;
using ZProjectBase.Mvc;
using <#=ProjectName#>.IRepository;
using <#=ProjectName#>.Entity.Models;

namespace <#=ProjectName#>.Repository
{   
    /// <summary>
    /// <#=tableName#>Repository (EF)
    /// </summary>
    public partial class <#=tableName#>Repository : DBContext<<#=tableName#>Model>, I<#=tableName#>Repository
    {
        public <#=tableName#>Repository(DbContextOptions<<#=tableName#>Repository> options) : base(options) { }

        public PageResult<<#=tableName#>Model> SelectPage(<#=tableName#>Params searchParams, PageInfo pageInfo)
        {
            var q = <#=tableName#>s.AsQueryable();
            var total = q.LongCount();
            var skip = pageInfo.page > 1 ? (pageInfo.page - 1) * pageInfo.limit : 0;
            var data = q.Skip(skip).Take(pageInfo.limit).ToList();
            return new PageResult<<#=tableName#>Model> { Data = data, Count = total };
        }

        <#
            var primaryKey = "";
            var primaryKey2 = "";
            var cSharpType = "";
            foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, tableName))
            {
                if(column.IsPrimaryKey) { cSharpType = column.CSharpType; primaryKey = column.ColumnName; primaryKey2 = column.ColumnName[0].ToString().ToLower() +  column.ColumnName.Substring(1); break; }
            }
            var camelCaseName = tableName[0].ToString().ToLower() + tableName.Substring(1);
            var modelName = tableName + "Model";
        #>
        <# if(cSharpType != "int"){ #>
        public <#=modelName#> Select<#=tableName#>(<#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey2==""?"id":primaryKey2#>)
        {
            return <#=tableName#>s.FirstOrDefault(x => x.<#=primaryKey2==""?"Id":primaryKey2#> == <#=primaryKey2==""?"id":primaryKey2#>);
        }
        <# } #>

        public bool Insert<#=tableName#>(<#=modelName#> <#=camelCaseName#>)
        {
            <#=tableName#>s.Add(<#=camelCaseName#>);
            return SaveChanges() > 0;
        }

        public bool Update<#=tableName#>(<#=modelName#> <#=camelCaseName#>)
        {
            <#=tableName#>s.Update(<#=camelCaseName#>);
            return SaveChanges() > 0;
        }

        public bool Delete<#=tableName#>(<#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey2==""?"id":primaryKey2#>)
        {
            <# if(cSharpType != "int"){ #>
            var entity = Select<#=tableName#>(<#=primaryKey2==""?"id":primaryKey2#>);
            if (entity == null) return false;
            <#=tableName#>s.Remove(entity);
            return SaveChanges() > 0;
            <# } else { #>
            var entity = <#=tableName#>s.FirstOrDefault(x => x.<#=primaryKey2==""?"Id":primaryKey2#> == <#=primaryKey2==""?"id":primaryKey2#>);
            if (entity == null) return false;
            <#=tableName#>s.Remove(entity);
            return SaveChanges() > 0;
            <# } #>
        }
    }
}
<# } else { #>
<#
    using(SqlConnection conn = new SqlConnection(config.ConnectionString)){
        conn.Open();
        var schema = conn.GetSchema("TABLES");
        foreach(DataRow row in schema.Rows){
            manager.StartBlock(row["TABLE_NAME"].ToString()+"Repository"+".cs", outputPath);
#>
using Microsoft.EntityFrameworkCore;
using ZProjectBase.DB.EF.Repository;
using ZProjectBase.Mvc;
using <#=ProjectName#>.IRepository;
using <#=ProjectName#>.Entity.Models;

namespace <#=ProjectName#>.Repository
{   
    /// <summary>
    /// <#=row["TABLE_NAME"].ToString()#>Repository (EF)
    /// </summary>
    public class <#=row["TABLE_NAME"].ToString()#>Repository : DBContext<<#=row["TABLE_NAME"].ToString()#>Model>, I<#=row["TABLE_NAME"].ToString()#>Repository
    {
        public <#=row["TABLE_NAME"].ToString()#>Repository(DbContextOptions<<#=row["TABLE_NAME"].ToString()#>Repository> options) : base(options) { }

        public PageResult<<#=row["TABLE_NAME"].ToString()#>Model> SelectPage(<#=row["TABLE_NAME"].ToString()#>Params searchParams, PageInfo pageInfo)
        {
            var q = <#=row["TABLE_NAME"].ToString()#>s.AsQueryable();
            var total = q.LongCount();
            var skip = pageInfo.page > 1 ? (pageInfo.page - 1) * pageInfo.limit : 0;
            var data = q.Skip(skip).Take(pageInfo.limit).ToList();
            return new PageResult<<#=row["TABLE_NAME"].ToString()#>Model> { Data = data, Count = total };
        }

        public <#=row["TABLE_NAME"].ToString()#>Model Select<#=row["TABLE_NAME"].ToString()#>(<#
            var primaryKey = "";
            var primaryKey2 = "";
            var cSharpType = "";
            foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, row["TABLE_NAME"].ToString()))
            {
                if(column.IsPrimaryKey) { cSharpType = column.CSharpType; primaryKey = column.ColumnName; primaryKey2 = column.ColumnName[0].ToString().ToLower() +  column.ColumnName.Substring(1); break; }
            }
        #><#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey2==""?"id":primaryKey2#>)
        {
            return <#=row["TABLE_NAME"].ToString()#>s.FirstOrDefault(x => x.<#=primaryKey==""?"Id":primaryKey#> == <#=primaryKey2==""?"id":primaryKey2#>);
        }

        public bool Insert<#=row["TABLE_NAME"].ToString()#>(<#=row["TABLE_NAME"].ToString()#>Model <#=row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1)#>)
        {
            <#=row["TABLE_NAME"].ToString()#>s.Add(<#=row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1)#>);
            return SaveChanges() > 0;
        }

        public bool Update<#=row["TABLE_NAME"].ToString()#>(<#=row["TABLE_NAME"].ToString()#>Model <#=row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1)#>)
        {
            <#=row["TABLE_NAME"].ToString()#>s.Update(<#=row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1)#>);
            return SaveChanges() > 0;
        }

        public bool Delete<#=row["TABLE_NAME"].ToString()#>(<#= (cSharpType==""?"object":cSharpType) #> <#=primaryKey2==""?"id":primaryKey2#>)
        {
            var entity = Select<#=row["TABLE_NAME"].ToString()#>(<#=primaryKey2==""?"id":primaryKey2#>);
            if (entity == null) return false;
            <#=row["TABLE_NAME"].ToString()#>s.Remove(entity);
            return SaveChanges() > 0;
        }
    }
}

//----------<#=row["TABLE_NAME"].ToString()#>结束----------
<#
            manager.EndBlock();
        }
        manager.Process(true);
    }
#>
<# } #>

