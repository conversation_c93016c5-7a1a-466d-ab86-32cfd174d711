<template>
  <div class="app-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form ref="queryForm" inline size="mini">
        <el-row :gutter="15">
          <el-form-item label="缓存键">
            <el-input v-model="queryParams.key" placeholder="请输入缓存键" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" icon="el-icon-search" @click="handleQuery"
              >查询</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 操作按钮 -->
      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="danger"
          icon="el-icon-delete"
          @click="handleDeleteSelected"
          :disabled="selectedKeys.length === 0"
          >批量删除</el-button
        >
      </el-row>

      <!-- 缓存数据展示 -->
      <div v-if="cacheData !== null">
        <el-card class="cache-result">
          <div slot="header" class="clearfix">
            <span>{{ queryParams.key }}</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="handleDeleteSingle(queryParams.key)"
              v-if="queryParams.key"
              >删除此缓存</el-button
            >
          </div>
          <div class="cache-content">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="缓存值">
                <div v-if="isJsonData(cacheData)" class="json-viewer">
                  <el-button
                    size="mini"
                    type="text"
                    @click="toggleJsonFormat"
                    style="margin-bottom: 10px"
                  >
                    {{ isFormatted ? '压缩显示' : '格式化显示' }}
                  </el-button>
                  <pre class="cache-value json-content">{{ formatJsonData(cacheData) }}</pre>
                </div>
                <div v-else>
                  <pre class="cache-value">{{ cacheData }}</pre>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </div>

      <!-- 查询历史记录 -->
      <div v-if="cacheKeys.length > 0" style="margin-top: 20px">
        <el-card>
          <div slot="header" class="clearfix">
            <span>查询历史</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="clearHistory"
              >清空历史</el-button
            >
          </div>
          <el-table :data="cacheKeys" @selection-change="handleSelectionChange" size="mini">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column type="index" label="序号" width="60"></el-table-column>
            <el-table-column prop="key" label="缓存键" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-link type="primary" @click="handleViewCache(scope.row.key)">
                  {{ scope.row.key }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="queryTime"
              label="查询时间"
              width="180"
              align="center"
            ></el-table-column>
            <el-table-column prop="hasData" label="状态" width="80" align="center">
              <template slot-scope="scope">
                <el-tag :type="scope.row.hasData ? 'success' : 'danger'" size="mini">
                  {{ scope.row.hasData ? '有数据' : '无数据' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
              <template slot-scope="scope">
                <el-button size="mini" type="primary" @click="handleViewCache(scope.row.key)"
                  >查看</el-button
                >
                <el-button size="mini" type="danger" @click="handleDeleteSingle(scope.row.key)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <!-- 空状态 -->
      <div
        v-if="(cacheData === null || cacheData === '') && cacheKeys.length === 0 && !loading"
        class="empty-state"
      >
        <div class="empty-content">
          <div class="empty-icon">
            <i class="el-icon-document" style="font-size: 64px; color: #dcdfe6"></i>
          </div>
          <div class="empty-text">
            <p class="empty-title">暂无数据</p>
            <p class="empty-description">暂无缓存数据，请输入缓存键进行查询</p>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as redisApi from '@/api/system/redis'

export default {
  name: 'RedisManage',
  data() {
    return {
      queryParams: {
        key: '',
      },
      loading: false,
      cacheData: null,
      cacheKeys: [], // 查询历史记录
      selectedKeys: [],
      isFormatted: true, // JSON格式化状态
    }
  },
  methods: {
    // 查询缓存
    async handleQuery() {
      if (!this.queryParams.key.trim()) {
        this.$xMsgWarning('请输入缓存键')
        return
      }

      this.loading = true
      try {
        const res = await redisApi.queryByKey(this.queryParams.key)
        if (res.code === 0) {
          this.cacheData = res.data
          // 添加到查询历史
          this.addToHistory(this.queryParams.key, res.data !== null && res.data !== undefined)
        } else {
          this.$xMsgError('查询失败：' + res.msg)
          this.cacheData = null
          // 添加到查询历史（查询失败）
          this.addToHistory(this.queryParams.key, false)
        }
      } catch (error) {
        this.$xMsgError('查询失败：' + error.message)
        this.cacheData = null
        this.addToHistory(this.queryParams.key, false)
      } finally {
        this.loading = false
      }
    },

    // 重置搜索
    handleReset() {
      this.queryParams.key = ''
      this.cacheData = null
      this.selectedKeys = []
    },

    // 查看缓存
    handleViewCache(key) {
      this.queryParams.key = key
      this.handleQuery()
    },

    // 删除单个缓存
    async handleDeleteSingle(key) {
      this.$confirm(`确定要删除缓存键 "${key}" 吗？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.loading = true
          try {
            const res = await redisApi.deleteByKey(key)
            if (res.code === 0) {
              this.$xMsgSuccess('删除成功')
              if (this.queryParams.key === key) {
                this.handleReset()
              }
              // 从历史记录中移除已删除的缓存键
              this.cacheKeys = this.cacheKeys.filter((item) => item.key !== key)
            } else {
              this.$xMsgError('删除失败：' + res.msg)
            }
          } catch (error) {
            this.$xMsgError('删除失败：' + error.message)
          } finally {
            this.loading = false
          }
        })
        .catch(() => {})
    },

    // 批量删除缓存
    async handleDeleteSelected() {
      if (this.selectedKeys.length === 0) {
        this.$xMsgWarning('请选择要删除的缓存')
        return
      }

      this.$confirm(`确定要删除选中的 ${this.selectedKeys.length} 个缓存吗？`, '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.loading = true
          try {
            const promises = this.selectedKeys.map((key) => redisApi.deleteByKey(key))
            await Promise.all(promises)
            this.$xMsgSuccess('批量删除成功')
            // 从历史记录中移除已删除的缓存键
            this.cacheKeys = this.cacheKeys.filter((item) => !this.selectedKeys.includes(item.key))
            this.selectedKeys = []
          } catch (error) {
            this.$xMsgError('批量删除失败：' + error.message)
          } finally {
            this.loading = false
          }
        })
        .catch(() => {})
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedKeys = selection.map((item) => item.key)
    },

    // 添加到查询历史
    addToHistory(key, hasData) {
      // 检查是否已存在
      const existingIndex = this.cacheKeys.findIndex((item) => item.key === key)
      const historyItem = {
        key: key,
        queryTime: new Date().toLocaleString(),
        hasData: hasData,
      }

      if (existingIndex !== -1) {
        // 更新现有记录
        this.$set(this.cacheKeys, existingIndex, historyItem)
      } else {
        // 添加新记录，最多保留10条
        this.cacheKeys.unshift(historyItem)
        if (this.cacheKeys.length > 10) {
          this.cacheKeys.pop()
        }
      }
    },

    // 清空历史记录
    clearHistory() {
      this.$confirm('确定要清空所有查询历史吗？', '提示', {
        type: 'warning',
      })
        .then(() => {
          this.cacheKeys = []
          this.selectedKeys = []
          this.$xMsgSuccess('历史记录已清空')
        })
        .catch(() => {})
    },

    // 判断是否为JSON数据
    isJsonData(data) {
      if (typeof data === 'object') {
        return true
      }
      if (typeof data === 'string') {
        try {
          JSON.parse(data)
          return true
        } catch (e) {
          return false
        }
      }
      return false
    },

    // 切换JSON格式化
    toggleJsonFormat() {
      this.isFormatted = !this.isFormatted
    },

    // 格式化JSON数据
    formatJsonData(data) {
      try {
        let jsonData = data
        if (typeof data === 'string') {
          jsonData = JSON.parse(data)
        }
        return this.isFormatted ? JSON.stringify(jsonData, null, 2) : JSON.stringify(jsonData)
      } catch (e) {
        return data
      }
    },
  },
}
</script>

<style scoped>
.cache-result {
  margin-top: 20px;
}

.cache-content {
  max-height: 500px;
  overflow-y: auto;
}

.cache-value {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 6px;
  font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  border: 1px solid #e4e7ed;
  max-height: 400px;
  overflow-y: auto;
}

.json-viewer {
  position: relative;
}

.json-content {
  background-color: #2d3748;
  color: #e2e8f0;
  border: 1px solid #4a5568;
}

.json-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-content::-webkit-scrollbar-track {
  background: #4a5568;
  border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb {
  background: #718096;
  border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.empty-state {
  margin-top: 40px;
  margin-bottom: 40px;
  text-align: center;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text {
  text-align: center;
}

.empty-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #909399;
  font-weight: 500;
}

.empty-description {
  margin: 0;
  font-size: 14px;
  color: #c0c4cc;
  line-height: 1.4;
}

.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
