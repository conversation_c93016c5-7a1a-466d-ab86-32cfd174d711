using ZProjectBase.Mvc;

namespace YYBM.Domain.Entity.ModelParams
{
    ///<summary>
    ///DomainOSSConfigParams
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-15 19:38:49 
    ///</summary>
    public partial class DomainOSSConfigParams : AdminRequestBase
    {

        /// <summary>
        /// Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// OSS桶保持个数
        /// </summary>
        public int? KeepCount { get; set; }

        /// <summary>
        /// 下线等待删除时间
        /// </summary>
        public int? OfflineDeleteTime { get; set; }

        /// <summary>
        /// 封禁等待删除时间
        /// </summary>
        public int? BannedDeleteTime { get; set; }

        /// <summary>
        /// 上线域名组Id
        /// </summary>
        public int? DomainGroupId { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }
    }
}

