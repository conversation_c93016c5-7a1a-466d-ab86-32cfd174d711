<template>
  <div class="table-cell-content">
    <el-link class="task-id" type="primary" :underline="false" @click="handleAccounts()">
      <div>{{ taskName }}</div>
    </el-link>
    <br />
    <span class="area-name">{{ areaName }}</span>
  </div>
</template>

<script>
export default {
  data: function () {
    return {}
  },
  props: ['taskId', 'taskName', 'areaName'],
  methods: {
    handleAccounts() {
      /*this.$router.push({
        path: '/deliver/deliverchannelaccount',
        query: { taskid: this.taskId, taskTitle: this.taskName },
      })*/
      window.open(
        `/deliver/deliverchannelaccount?taskid=${this.taskId}&taskTitle=${encodeURIComponent(
          this.taskName
        )}`
      )
    },
  },
}
</script>

<style scoped>
/* 样式1：调整字体大小、行高、间距 */
.table-cell-content {
  display: flex;
  flex-direction: column;
  align-items: center; /* 中心对齐 */
  font-size: 14px; /* 设置字体大小 */
  line-height: 1; /* 行高 */
  word-break: break-word; /* 防止长单词溢出 */
  color: #333; /* 设置字体颜色 */
  padding: 5px 0; /* 设置内边距 */
}

.task-id {
  font-weight: bold; /* scId 字段加粗 */
  color: #409eff; /* 修改颜色 */
}

.area-name {
  font-style: italic; /* scName 字段斜体 */
  color: #091a01; /* 修改颜色 */
}

/* 样式2：给换行部分添加背景色 */
.table-cell-content span {
  background-color: #f2f4f5; /* 设置背景色 */
  padding: 2px 4px; /* 给文字加点内边距 */
  border-radius: 4px; /* 设置圆角 */
}
</style>
