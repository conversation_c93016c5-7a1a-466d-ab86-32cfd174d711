﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace YYBM.Repository.Extensions
{
    public static class RepositoryServiceCollectionExtensions
    {
        /// <summary>
        /// 同时注册 EF Core DbContext 与接口映射（推荐用于当前项目）
        /// </summary>
        public static IServiceCollection AddYYBMEfRepositories(this IServiceCollection services, IConfiguration configuration, string connectionStringName = "YYBMConnstr", ServiceLifetime lifetime = ServiceLifetime.Scoped)
        {
            var connStr = configuration.GetConnectionString(connectionStringName);
            if (string.IsNullOrWhiteSpace(connStr)) throw new InvalidOperationException($"Connection string '{connectionStringName}' not found.");

            var assembly = Assembly.GetExecutingAssembly();
            var repoTypes = assembly
                .GetTypes()
                .Where(t => !t.IsAbstract && t.IsClass && t.Name.EndsWith("Repository") && typeof(DbContext).IsAssignableFrom(t));

            foreach (var repoType in repoTypes)
            {
                // 通过反射调用泛型的 AddDbContext<TContext>()
                RegisterDbContextGeneric(services, repoType, connStr);

                services.Add(new ServiceDescriptor(repoType, repoType, lifetime));

                var interfaces = repoType.GetInterfaces().Where(i => i.Name.EndsWith("Repository"));
                foreach (var iface in interfaces)
                {
                    services.Add(new ServiceDescriptor(iface, provider => provider.GetRequiredService(repoType), lifetime));
                }
            }
            return services;
        }

        private static void RegisterDbContextGeneric(IServiceCollection services, Type repoType, string connStr)
        {
            // 构造 options 配置委托
            Action<DbContextOptionsBuilder> configure = options =>
            {
                options.UseSqlServer(connStr);
#if DEBUG
                options.EnableDetailedErrors();
                options.EnableSensitiveDataLogging();
#endif
            };

            var methods = typeof(EntityFrameworkServiceCollectionExtensions)
                .GetMethods(BindingFlags.Public | BindingFlags.Static)
                .Where(m => m.Name == "AddDbContext" && m.IsGenericMethodDefinition)
                .ToList();

            // 选择具有签名 (IServiceCollection, Action<DbContextOptionsBuilder>, ServiceLifetime, ServiceLifetime) 的重载
            var target = methods
                .First(m =>
                {
                    var ps = m.GetParameters();
                    return ps.Length == 4 && ps[0].ParameterType == typeof(IServiceCollection) && ps[1].ParameterType == typeof(Action<DbContextOptionsBuilder>);
                });

            var generic = target.MakeGenericMethod(repoType);
            generic.Invoke(null, new object[] { services, configure, ServiceLifetime.Scoped, ServiceLifetime.Scoped });
        }
    }
}
