<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="标题">
          <el-input v-model="queryParams.title" placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item label="发布人">
          <x-select v-model="queryParams.publisher" url="/system/user/options"></x-select>
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker
            v-model="queryParams.createOn"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="createOnTimePickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['systemNotice:add']"
          >新增</el-button
        >
        <el-button
          size="mini"
          type="info"
          @click="handleReadAll"
          v-permission="['systemNotice:detail']"
          >一键已读</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        @sort-change="handleSortChange"
      >
        <el-table-column type="index" label="序号" align="center" min-width="50"></el-table-column>
        <el-table-column label="状态" align="center" min-width="100">
          <template v-slot="{ row }">
            <el-tag type="success" size="medium" v-if="row.isRead">已读</el-tag>
            <el-tag type="warning" size="medium" v-else>未读</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" align="center" min-width="350"></el-table-column>
        <el-table-column
          prop="publisherName"
          label="发布管理员"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="createOn"
          label="发布时间"
          align="center"
          min-width="180"
          sortable="custom"
        ></el-table-column>
        <el-table-column label="操作" min-width="160" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleRead(scope.row)"
              v-permission="['systemNotice:detail']"
              >已读</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['systemNotice:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['systemNotice:del']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { commonTimePickerOptions } from '@/utils/options'
import { tableHeightMixin } from '@/mixin'
import { systemNoticeApi } from '@/api'
import EditDialog from './Edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      createOnTimePickerOptions: commonTimePickerOptions,
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (params.createOn) {
        params.beginTime = params.createOn[0]
        params.endTime = params.createOn[1]
        delete params.createOn
      }
      const res = await systemNoticeApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleRead(row) {
      this.$xloading.show()
      const res = await systemNoticeApi.readNotice(row.snuId, 1)
      this.$xloading.hide()
      if (res.code == 0) {
        this.$refs.table.refresh()
        this.$xMsgSuccess('操作成功')
      } else {
        this.$xMsgError('操作失败！' + res.msg)
      }
    },
    async handleReadAll() {
      this.$xloading.show()
      const res = await systemNoticeApi.readAll()
      this.$xloading.hide()
      if (res.code == 0) {
        this.$refs.table.refresh()
        this.$xMsgSuccess('操作成功')
        window.location.reload()
      } else {
        this.$xMsgError('操作失败！' + res.msg)
      }
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await systemNoticeApi.delSystemNotice(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    handleSortChange({ order, prop }) {
      if (!prop) {
        return
      }
      console.log(order, prop)
      this.queryParams.field = prop
      this.queryParams.order = order == 'ascending' ? 'asc' : 'desc'
      this.$refs.table.refresh(true)
    },
  },
}
</script>
