﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","IRepositories");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}
#>

//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//	   生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# 
	var tableName=config.TableName;
#>
<# 
if(tableName!=""){
#>  
using ZProjectBase.DB.IRepository;
using <#=ProjectName#>.Entity.Models;
using ZProjectBase.Mvc;

namespace <#=ProjectName#>.IRepository
{	
	/// <summary>
	/// I<#=tableName#>Repository
	///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	/// </summary>	
	public partial interface I<#=tableName#>Repository : IBaseRepository<<#=tableName#>>
    {
      
    }
}

<# 
} else{ 
#>
<# 
    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
#>

<# 
foreach(System.Data.DataRow row in schema.Rows) 
{  
	manager.StartBlock("I"+row["TABLE_NAME"].ToString()+"Repository"+".cs",outputPath);//文件名
    var primaryKey = "";
    var cSharpType = "";   
    var deleteMethodParams = "";
    foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, row["TABLE_NAME"].ToString()))
    {
         if(column.IsPrimaryKey) 
         {
            cSharpType = column.CSharpType;
            primaryKey = column.ColumnName;
            deleteMethodParams = "("+ cSharpType +" "+ primaryKey +")";
         } 
     }
	var tableNameModel = row["TABLE_NAME"].ToString()+ "Model";
    var paramsName = row["TABLE_NAME"].ToString() + "Params";
    var camelCaseName = row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1);
    var interfaceName = "I" + row["TABLE_NAME"].ToString() + "Repository";
    var selectMethod = "Select" + row["TABLE_NAME"].ToString() + deleteMethodParams;
    var insertMethod = "Insert" + row["TABLE_NAME"].ToString() + "(";
    var updateMethod = "Update" + row["TABLE_NAME"].ToString() + "(";
    var deleteMethod = "Delete" + row["TABLE_NAME"].ToString() + deleteMethodParams;
    var idName = camelCaseName + ".Id";
	#>
//----------<#=row["TABLE_NAME"].ToString()#>开始----------
    
using ZProjectBase.DB.IRepository;
using <#=ProjectName#>.Entity.Models;
using ZProjectBase.Mvc;

namespace <#=ProjectName#>.IRepository
{	
	/// <summary>
	/// I<#=row["TABLE_NAME"].ToString()#>Repository
	///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	/// </summary>	
	public interface I<#=row["TABLE_NAME"].ToString()#>Repository : IBaseRepository<<#=tableNameModel#>>//类名
    {
	    /// <summary>
        /// 分页查询
        /// </summary>
        PageResult<<#=tableNameModel#>> SelectPage(<#=paramsName#> searchParams, PageInfo pageInfo);
        <# if(cSharpType != "int"){ #>

        /// <summary>
        /// 查询
        /// </summary>
        <#=tableNameModel#> <#=selectMethod#>;
        <#}#>

        /// <summary>
        /// 新增
        /// </summary>
        bool <#=insertMethod#><#=tableNameModel#> <#=camelCaseName#>);

        /// <summary>
        /// 更新
        /// </summary>
        bool <#=updateMethod#><#=tableNameModel#> <#=camelCaseName#>);

        /// <summary>
        /// 删除
        /// </summary>
        bool <#=deleteMethod#>;   
    }
}

//----------<#=row["TABLE_NAME"].ToString()#>结束----------  
<# 
	manager.EndBlock(); 
	} 
	manager.Process(true);
}
#> 