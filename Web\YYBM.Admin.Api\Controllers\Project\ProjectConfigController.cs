﻿using Microsoft.AspNetCore.Mvc;
using YYBM.Admin.IService;
using YYBM.Entity.ModelParams;
using YYBM.Entity.VOs;
using ZProjectBase.Admin.Core.Controllers;
using ZProjectBase.Admin.Core.Filters;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Api.Controllers.Project
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProjectConfigController : BaseApiController
    {
        private readonly IProjectConfigService _projectConfigService;

        public ProjectConfigController(IProjectConfigService projectConfigService)
        {
            _projectConfigService = projectConfigService;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        [HttpGet("GetList")]
        [Permission("projectConfig:list")]
        public TableResult<ProjectConfigVO> GetList([FromQuery] ProjectConfigParams searchParams)
        {
            return _projectConfigService.GetTableList(searchParams);
        }
        /// <summary>
        /// 详情
        /// </summary>
        [HttpGet("GetDetail")]
        [Permission("projectConfig:detail")]
        public ResponseResult<ProjectConfigVO> GetDetail([FromQuery] int id)
        {
            return new ResponseResult<ProjectConfigVO>(_projectConfigService.GetDetail(id));
        }
        /// <summary>
        /// 新增
        /// </summary>
        [HttpPost("Add")]
        [Permission("projectConfig:add")]
        public ResponseResult Add([FromBody] ProjectConfigParams ProjectConfigParams)
        {
            return _projectConfigService.AddProjectConfig(ProjectConfigParams);
        }
        /// <summary>
        /// 修改
        /// </summary>
        [HttpPost("Edit")]
        [Permission("projectConfig:edit")]
        public ResponseResult Edit([FromBody] ProjectConfigParams ProjectConfigParams)
        {
            return _projectConfigService.UpdateProjectConfig(ProjectConfigParams);
        }
        /// <summary>
        /// 删除
        /// </summary>
        [HttpPost("Delete")]
        [Permission("projectConfig:delete")]
        public ResponseResult Delete([FromBody] ProjectConfigParams ProjectConfigParams)
        {
            return _projectConfigService.DeleteProjectConfig(ProjectConfigParams);
        }
    }
}
