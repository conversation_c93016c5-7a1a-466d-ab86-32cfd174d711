﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZProjectBase.Admin.Core.DTOs;

namespace YYBM.Admin.IService
{
    public interface IOptionsService
    {
        List<OptionItem<int>> GetCloudPlatformOptions();
        List<OptionItem<int>> GetProjectOptions();
        List<OptionItem<int>> GetDomainGroupOptions();
        List<OptionItem<int>> GetDomainOSSConfigOptions();
        List<OptionGroupItem<int>> GetCloudAccountOptions(int? platform);
        List<OptionItem<int>> GetBucketStatusOptions();
    }
}
