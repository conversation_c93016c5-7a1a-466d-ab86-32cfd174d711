﻿using YYBM.Cloud.Entity.Enums;
using YYBM.Cloud.Entity.ModelParams;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.IRepository;
using YYBM.Cloud.ObjectStorage.Interfaces;
using YYBM.Cloud.Repository;
using YYBM.Cloud.Service.ObjectStorage.Mappers;
using YYBM.Cloud.Service.ObjectStorage.Providers.Aliyun;
using YYBM.Cloud.Service.ObjectStorage.Providers.ECloud;
using YYBM.Domain.IRepository;
using ZProjectBase.Common.Extend;
using ZProjectBase.Common.Log;
using ZProjectBase.Common.Utils;

namespace YYBM.Cloud.Service.ObjectStorage.Abstractions
{
    public abstract class BaseObjectStorageClient
    {
        protected abstract string DomainTemplate { get; }
        protected abstract IObjectStorageService _ossClient { get; }
        protected abstract CloudPlatform _platform { get; }

        protected readonly IOSSConfigRepository _ossConfigRepository;
        protected readonly IOSSBucketRepository _ossBucketRepository;
        protected readonly IDomainOSSConfigRepository _domainOSSConfigRepository;
        protected readonly IAccountRepository _accountRepository;

        protected BaseObjectStorageClient(
            IOSSConfigRepository ossConfigRepository,
            IOSSBucketRepository ossBucketRepository,
            IDomainOSSConfigRepository domainOSSConfigRepository,
            IAccountRepository accountRepository)
        {
            _ossConfigRepository = ossConfigRepository;
            _ossBucketRepository = ossBucketRepository;
            _domainOSSConfigRepository = domainOSSConfigRepository;
            _accountRepository = accountRepository;
        }

        public async Task Run(int? projId = null)
        {
            var ossConfigs = await _ossConfigRepository.GetAllAsync();

            if (projId.HasValue)
                ossConfigs = ossConfigs.Where(x => x.ProjId == projId.Value);

            ossConfigs = ossConfigs?.Where(x => x.Enable && x.Platform == _platform);

            if (ossConfigs == null || !ossConfigs.Any())
            {
                return;
            }

            await OssConfigProcess(ossConfigs);
        }

        private async Task OssConfigProcess(IEnumerable<OSSConfigModel> ossConfigs)
        {
            foreach (var ossConfig in ossConfigs)
            {
                var domainOSSConfig = await _domainOSSConfigRepository.SelectAsync(ossConfig.DomainOssConfigId);
                if (domainOSSConfig == null)
                {
                    L4NLog.Warn($"DomainOSSConfigId is null for OSSConfigId: {ossConfig.Id}");
                    continue;
                }

                var param = new OSSBucketParams()
                {
                    OSSConfigId = ossConfig.Id,
                    Status = (int)BucketStatus.Used
                };
                var ossBuckets = await _ossBucketRepository.GetListAsync(param);

                var difference = domainOSSConfig.KeepCount - ossBuckets.Count();
                if (difference <= 0)
                {
                    L4NLog.Debug($"OSSConfigId: {ossConfig.Id},OSS在线桶数量已经达到限制数{domainOSSConfig.KeepCount}");
                    continue;
                }

                // 遍历差值，创建新的桶
                for (int i = 0; i < difference; i++)
                {
                    var acountsWeightConfig = AccountsWeightConfigMapper.GetRandomAccount(ossConfig);
                    var regionEndpointsConfig = RegionEndpointsConfigMapper.GetRandomRegionEndpointConfig(ossConfig);

                    if (acountsWeightConfig == null || regionEndpointsConfig == null)
                    {
                        L4NLog.Warn($"无法获取随机账户或区域端点配置，OSSConfigId: {ossConfig.Id}");
                        continue;
                    }

                    await CreateBucketAsync(ossConfig, acountsWeightConfig, regionEndpointsConfig);
                }
            }
        }

        private async Task CreateBucketAsync(OSSConfigModel ossConfig, AccountsWeightConfig accountsWeightConfig, RegionEndpointsConfig regionEndpointsConfig)
        {
            var bucketName = CreateBuckeName();
            var account = await _accountRepository.SelectAsync(accountsWeightConfig.AccountId);
            if (account == null)
            {
                L4NLog.Warn($"Account not found for AccountId: {accountsWeightConfig.AccountId}");
                return;
            }

            var accountVO = MapperUtil.MapperIns.Map<AccountVO>(account);
            accountVO.Region = regionEndpointsConfig.Region;
            accountVO.Endpoint = regionEndpointsConfig.Endpoint;

            if (ossConfig.Platform == CloudPlatform.Aliyun)
            {
                var aliyunClient = AliyunCreator.GetInstance();
                var res = aliyunClient.CreateBucket(bucketName, accountVO);
                if (res == false)
                {
                    L4NLog.Warn($"Created bucket failed AccountId: {accountsWeightConfig.AccountId}");
                    return;
                }
            }

            if (ossConfig.Platform == CloudPlatform.ECloud)
            {
                var ecloudClient = ECloudCreator.GetInstance();
                var res = ecloudClient.CreateBucket(bucketName, accountVO);
                if (res == false)
                {
                    L4NLog.Warn($"Created bucket failed AccountId: {accountsWeightConfig.AccountId}");
                    return;
                }
            }
            await CreateOssBucket(ossConfig, accountsWeightConfig, regionEndpointsConfig, bucketName);
            await CreateObjectsAsync(bucketName, ossConfig, accountVO);
            await CreateStaticWebsitAsync(bucketName, ossConfig, accountVO);
        }

        private async Task CreateOssBucket(OSSConfigModel ossConfig, AccountsWeightConfig accountsWeightConfig, RegionEndpointsConfig regionEndpointsConfig, string bucketName)
        {
            var ossBucket = new OSSBucketModel()
            {
                Bucket = bucketName,
                ProjId = ossConfig.ProjId,
                Platform = (int)ossConfig.Platform,
                AccountId = accountsWeightConfig.AccountId,
                OSSConfigId = ossConfig.Id,
                Status = BucketStatus.Used,
                Location = regionEndpointsConfig.Region,
                CreateOn = DateTime.Now
            };
            await _ossBucketRepository.CreateAsync(ossBucket);
        }

        public async Task CreateObjectsAsync(string bucketName, OSSConfigModel ossConfig, AccountVO accountVO)
        {
            var filesPathConfigs = FilesPathConfigMapper.MapToFilesPathConfigList(ossConfig);
            if (filesPathConfigs.Count <= 0)
            {
                L4NLog.Warn("FilesPathConfig is empty, no files to create.");
                return;
            }

            foreach (var filesPathConfig in filesPathConfigs)
            {
                if (filesPathConfig.IsDirectory)
                {
                    var files = Directory.GetFiles(filesPathConfig.Path, "*", SearchOption.AllDirectories);
                    foreach (string file in files)
                    {
                        filesPathConfig.Path = file;
                        var parentDirectoryPath = Path.GetDirectoryName(file); // 上一级目录路径
                        var parentDirectoryName = Path.GetFileName(parentDirectoryPath); // 上一级目录名称
                        var fileName = parentDirectoryName + "/" + Path.GetFileName(file);

                        var res = await UploadFileOrCreateDomainAsync(bucketName, fileName, ossConfig, accountVO, filesPathConfig);
                        if (!res)
                        {
                            _ossBucketRepository.UpdateStatus(bucketName, BucketStatus.Failure);
                        }
                    }
                }
                else
                {
                    var fileName = filesPathConfig.Key.IsNullOrEmpty() ? Path.GetFileName(filesPathConfig.Path) : filesPathConfig.Key;
                    if (fileName.IsNullOrEmpty())
                    {
                        L4NLog.Warn($"File name is empty for path: {filesPathConfig.Path}");
                        continue;
                    }

                    var res = await UploadFileOrCreateDomainAsync(bucketName, fileName, ossConfig, accountVO, filesPathConfig);
                    if (!res)
                    {
                        _ossBucketRepository.UpdateStatus(bucketName, BucketStatus.Failure);
                    }
                }
            }
        }

        public abstract Task<bool> UploadFileOrCreateDomainAsync(string bucketName, string fileName, OSSConfigModel ossConfig, AccountVO accountVO, FilesPathConfig filesPathConfig);

        public abstract Task CreateStaticWebsitAsync(string bucketName, OSSConfigModel ossConfig, AccountVO accountVO);

        public bool UploadFile(string bucketName, string fileName, string filePath, AccountVO accountVO)
        {
            return _ossClient.PutObjectFromFile(bucketName, fileName, filePath, accountVO);
        }

        private string CreateBuckeName()
        {
            var guid = RandomUtils.GenerateRandomString(14, 18);
            return $"{guid.ToLower()}";
        }
    }
}
