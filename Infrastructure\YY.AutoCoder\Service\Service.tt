﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","Services");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}
#>

//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//	   生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# 
	var tableName=config.TableName;
 #>
<# 
if(tableName!=""){
#>  

using ZProjectBase.Admin;
using ZProjectBase.DB.Service;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Mvc;
using ZProjectBase.Common.Utils;
using <#=ProjectName#>.IService;
using <#=ProjectName#>.Entity.Models;
using <#=ProjectName#>.Entity.ModelParams;
using <#=ProjectName#>.Entity.VOs;
using <#=ProjectName#>.IRepository;

namespace <#=ProjectName#>.Service
{	
	/// <summary>
	/// <#=tableName#>Service
    ///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	///  对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
	/// </summary>	
	public partial class <#=tableName#>Service : BaseServices<<#=tableName#>>
    {
	
        I<#=tableName#>Repository dal;
        public <#=tableName#>Service(I<#=tableName#>Repository dal)
        {
            this.dal = dal;
            base.baseDal = dal;
        }
       
    }
}

<# 
} else{ 
#>

<# 
    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
#>

<# 
foreach(System.Data.DataRow row in schema.Rows) 
{  
    var primaryKey = "";
    var cSharpType = "";   
    var selectMethodParams = "";
	manager.StartBlock(row["TABLE_NAME"].ToString()+"Service"+".cs",outputPath);
    foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, row["TABLE_NAME"].ToString()))
    {
         if(column.IsPrimaryKey) 
         {
            cSharpType = column.CSharpType;
            primaryKey = column.ColumnName;
            selectMethodParams = "("+ cSharpType +" "+ primaryKey +")";
         }
     }
    var tableNameModel = row["TABLE_NAME"].ToString()+ "Model";
    var camelCaseName = row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1);
    var interfaceName = "I" + row["TABLE_NAME"].ToString() + "Service";
    var tableListMethod = "";
    var selectMethod = "Select" + row["TABLE_NAME"].ToString();
    var insertMethod = "Add" + row["TABLE_NAME"].ToString() + "(";
    var updateMethod = "Update" + row["TABLE_NAME"].ToString() + "(";
    var deleteMethod = "Delete" + row["TABLE_NAME"].ToString();
    var voName = row["TABLE_NAME"].ToString() + "VO";
    var paramsName = row["TABLE_NAME"].ToString() + "Params";
    var iRepositoryName = "I" + row["TABLE_NAME"].ToString() + "Repository";
    var repositoryName =camelCaseName + "Repository";
    var constructorName = string.Format("public {0}(IHttpContextAccessor httpContextAccessor,I{1}Repository {2}) : base(httpContextAccessor)", row["TABLE_NAME"].ToString()+"Service", row["TABLE_NAME"].ToString(), repositoryName);
	#>
//----------<#=row["TABLE_NAME"].ToString()#>开始----------

using ZProjectBase.Admin;
using ZProjectBase.DB.Service;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Mvc;
using ZProjectBase.Common.Utils;
using <#=ProjectName#>.IService;
using <#=ProjectName#>.Entity.Models;
using <#=ProjectName#>.Entity.ModelParams;
using <#=ProjectName#>.Entity.VOs;
using <#=ProjectName#>.IRepository;

namespace <#=ProjectName#>.Service
{	
	/// <summary>
	/// <#=row["TABLE_NAME"].ToString()#>Service
    ///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	/// </summary>	
	public class <#=row["TABLE_NAME"].ToString()#>Service : AdminServiceBase<<#=tableNameModel#>>,I<#=row["TABLE_NAME"].ToString()#>Service
    {
	
        private readonly <#=iRepositoryName#> _<#=repositoryName#>;

        <#=constructorName#>
        {
            _<#=repositoryName#> = <#=repositoryName#>;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<<#=voName#>> GetTableList(<#=paramsName#> searchParams)
        {
            var pageResult = _<#=repositoryName#>.SelectPage(searchParams, searchParams.ToPageInfo());
            return CreateTableResult<<#=tableNameModel#>, <#=voName#>>(pageResult);
        }

        <# if(cSharpType != "int"){ #>

        /// <summary>
        /// 查询
        /// </summary>
        public <#=voName#> GetDetail<#=selectMethodParams#>
        {
            var <#=camelCaseName#> = _<#=repositoryName#>.<#=selectMethod#>(<#=primaryKey#>);
            if (<#=camelCaseName#> == null)
            {
                throw new BusinessException("数据不存在");
            }
            var <#=camelCaseName#>VO = MapperIns.Map<<#=voName#>>(<#=camelCaseName#>);
            return <#=camelCaseName#>VO;
        }
        <#} else{#>

        /// <summary>
        /// 详情
        /// </summary>
        public <#=voName#> GetDetail(int Id)
        {
            var <#=camelCaseName#> = _<#=repositoryName#>.Select(Id);
            if (<#=camelCaseName#> == null)
            {
                throw new BusinessException("数据不存在");
            }
            var <#=camelCaseName#>VO = MapperIns.Map<<#=voName#>>(<#=camelCaseName#>);
            return <#=camelCaseName#>VO;
        }
        <#}#>

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult <#=insertMethod#><#=paramsName#> <#=camelCaseName#>Params)
        {
            var <#=camelCaseName#> = MapperIns.Map<<#=tableNameModel#>>(<#=camelCaseName#>Params);
            var result = _<#=repositoryName#>.Insert<#=row["TABLE_NAME"].ToString()#>(<#=camelCaseName#>);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult <#=updateMethod#><#=paramsName#> <#=camelCaseName#>Params)
        {
            var <#=camelCaseName#> = MapperIns.Map<<#=tableNameModel#>>(<#=camelCaseName#>Params);
            var result = _<#=repositoryName#>.Update<#=row["TABLE_NAME"].ToString()#>(<#=camelCaseName#>);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult <#=deleteMethod#>(<#=paramsName#> <#=camelCaseName#>Params)
        {
<# 
            if(primaryKey == "")
            {
#>
            throw new NotImplementedException();
<#
            }
            else {
#>
<# if(cSharpType == "int"){ #>
            CheckParamsUtils.Failure(<#=camelCaseName#>Params.<#=primaryKey#> == 0, "<#=primaryKey#>不能为空");
<#}
            else{ 
#>
            CheckParamsUtils.Failure(string.IsNullOrEmpty(<#=camelCaseName#>Params.<#=primaryKey#>), "<#=primaryKey#>不能为空");
<#}#> 
            var result = _<#=repositoryName#>.Delete<#=row["TABLE_NAME"].ToString()#>(<#=camelCaseName#>Params.<#=primaryKey#>);
            return ResponseResult.Result(result);
<#}#>
        }
       
    }
}

//----------<#=row["TABLE_NAME"].ToString()#>结束----------
<# 
	manager.EndBlock(); 
	} 
	manager.Process(true);
}
#> 