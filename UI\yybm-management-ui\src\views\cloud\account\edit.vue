<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24">
          <el-form-item label="账号Id" prop="id">
            <el-input :disabled="true" v-model="form.id" placeholder="Id自动生成" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="账号名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="秘钥Id" prop="secretId">
            <el-input v-model="form.secretId" placeholder="请输入秘钥Id" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="秘钥" prop="secretKey">
            <el-input v-model="form.secretKey" placeholder="请输入秘钥" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="最大桶数" prop="bucketLimit">
            <el-input-number v-model="form.bucketLimit" placeholder="请输入最大桶数" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="云平台" prop="platform">
            <x-radio
              button
              v-model="form.platform"
              url="/options/getCloudPlatformOptions"
            ></x-radio>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input
              type="textarea"
              rows="3"
              v-model="form.description"
              placeholder="请输入描述"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { accountApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await accountApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await accountApi.editAccount(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await accountApi.addAccount(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
