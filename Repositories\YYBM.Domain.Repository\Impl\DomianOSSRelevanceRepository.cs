//----------DomianOSSRelevance开始----------   

using Microsoft.Extensions.Configuration;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.IRepository;
using YYBM.Domain.Repository.SqlDb;
using ZProjectBase.Mvc;

namespace YYBM.Domain.Repository
{
    /// <summary>
    /// DomianOSSRelevanceRepository
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-19 16:24:29 
    /// </summary>	
    public class DomianOSSRelevanceRepository : YYBMDomainRepository<DomianOSSRelevanceModel>, IDomianOSSRelevanceRepository
    {
        public DomianOSSRelevanceRepository(IConfiguration configuration) : base(configuration)
        {
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public PageResult<DomianOSSRelevanceModel> SelectPage(DomianOSSRelevanceParams searchParams, PageInfo pageInfo)
        {
            var pageResult = new PageResult<DomianOSSRelevanceModel>();
            long total;
            pageResult.Data = conn.GetByPage<DomianOSSRelevanceModel>(pageInfo.page, pageInfo.limit, out total, param: searchParams);
            pageResult.Count = total;
            return pageResult;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public bool InsertDomianOSSRelevance(DomianOSSRelevanceModel domianOSSRelevance)
        {
            var res = conn.Insert(domianOSSRelevance) ?? 0;
            return res > 0;
        }
        /// <summary>
        /// 更新
        /// </summary>
        public bool UpdateDomianOSSRelevance(DomianOSSRelevanceModel domianOSSRelevance)
        {
            return conn.UpdateById(domianOSSRelevance) > 0;
        }
        /// <summary>
        /// 删除
        /// </summary>
        public bool DeleteDomianOSSRelevance(int Id)
        {
            return conn.DeleteById<DomianOSSRelevanceModel>(Id) > 0;
        }
    }
}

//----------DomianOSSRelevance结束----------

