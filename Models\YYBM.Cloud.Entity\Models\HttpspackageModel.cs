using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Cloud.Entity.Models
{
    ///<summary>
    ///Httpspackage
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-12 14:57:19 
    ///</summary>
    [Table("Httpspackage")]
    public partial class HttpspackageModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(true)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// 平台，1：腾讯云
        /// </summary>
        [Required]
        public int Platform { get; set; }

        /// <summary>
        /// 账号Id
        /// </summary>
        [Required]
        public int AccountId { get; set; }

        /// <summary>
        /// Https次数
        /// </summary>
        public int? Size { get; set; }

        /// <summary>
        /// 已经使用的Https次数
        /// </summary>
        public int? SizeUsed { get; set; }

        /// <summary>
        /// LastUpdateTime
        /// </summary>
        public DateTime? LastUpdateTime { get; set; }

        /// <summary>
        /// 再用个数
        /// </summary>
        public int EnableCount { get; set; }
    }
}

