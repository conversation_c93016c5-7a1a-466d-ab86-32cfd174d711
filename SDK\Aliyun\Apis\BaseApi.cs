﻿using Newtonsoft.Json;

namespace Aliyun.Apis
{
    public class BaseApi
    {
        protected static HttpClient Client { get; set; }
        protected int _httpTimeout = 10 * 1000;

        public BaseApi()
        {
            if (Client == null)
            {
                Client = new HttpClient
                {
                    Timeout = TimeSpan.FromMilliseconds(_httpTimeout)
                };
            }
        }

        protected T Deserialize<T>(string str)
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(str);
            }
            catch { }
            return default(T);
        }
    }
}
