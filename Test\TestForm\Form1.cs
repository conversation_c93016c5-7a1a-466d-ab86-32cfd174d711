using TencentCloud.Apis.CDN;
using ZProjectBase.Common;

namespace TestForm
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private async void btn_DescribeTrafficPackagesApi_Click(object sender, EventArgs e)
        {
            var secretId = "AKIDPipjSzAYXLwLXXQ3f7NRIyRinfXEq6FC";
            var secretKey = "xJTRDnALJ5f2A7sQnymSwDJDuOgmgNrD";
            DescribeTrafficPackagesApi describeTrafficPackagesApi = new DescribeTrafficPackagesApi();
            var res = await describeTrafficPackagesApi.GetDescribeTrafficPackages(secretId, secretKey);
            Console.Write(res.ToJson());
        }

        private async void btn_DescribeHttpsPackagesApi_Click(object sender, EventArgs e)
        {
            var secretId = "AKIDPipjSzAYXLwLXXQ3f7NRIyRinfXEq6FC";
            var secretKey = "xJTRDnALJ5f2A7sQnymSwDJDuOgmgNrD";
            DescribeHttpsPackagesApi describeHttpsPackagesApi = new DescribeHttpsPackagesApi();
            var res = await describeHttpsPackagesApi.GetDescribeHttpsPackages(secretId, secretKey);
            Console.Write(res.ToJson());
        }
    }
}
