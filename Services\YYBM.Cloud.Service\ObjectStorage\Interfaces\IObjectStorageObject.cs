﻿using YYBM.Cloud.Entity.VOs;
using YYBM.Cloud.Service.ObjectStorage.DTOs;

namespace YYBM.Cloud.Service.ObjectStorage.Interfaces
{
    public interface IObjectStorageObject
    {
        IEnumerable<OssObject> GetObjects(string bucketName, AccountVO accountVO);

        OssObject GetObject(string bucketName, string objectKey, AccountVO accountVO);

        bool PutObjectFromFile(string bucketName, string key, string filePath, AccountVO accountVO);

        bool DoesObjectExist(string bucketName, string key, AccountVO accountVO);

        bool DeleteObject(string bucketName, string key, AccountVO accountVO);

        bool DeleteObjects(string bucketName, AccountVO accountVO);
    }
}
