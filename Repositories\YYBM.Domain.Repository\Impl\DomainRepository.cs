//----------Domain开始----------   

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System.Text;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.IRepository;
using YYBM.Domain.Repository.SqlDb;
using ZProjectBase.DB.EF.Repository;
using ZProjectBase.DB.Extend;
using ZProjectBase.Mvc;

namespace YYBM.Domain.Repository
{
    /// <summary>
    /// DomainRepository (EF)
    /// </summary>
    public class DomainRepository : YYBMDomainContext<DomainModel>, IDomainRepository
    {
        public DomainRepository(DbContextOptions<DomainRepository> options) : base(options) { }

        public PageResult<DomainModel> SelectPage(DomainParams searchParams, PageInfo pageInfo)
        {
            var q = from d in Domains
                    join dg in DomainGroups on d.GroupId equals dg.Id
                    where
                        (searchParams.Id <= 0 || d.Id == searchParams.Id) &&
                        (searchParams.Domain.IsNullOrEmpty() || d.Domain.Equals(searchParams.Domain)) &&
                        (searchParams.ProjId <= 0 || d.ProjId == searchParams.ProjId) &&
                        (searchParams.GroupId <= 0 || d.GroupId == searchParams.GroupId) &&
                        (searchParams.State.HasValue == false || (int)d.State == searchParams.State.Value)
                    orderby d.CreateOn descending
                    select new DomainModel
                    {
                        Id = d.Id,
                        Domain = d.Domain,
                        ProjId = d.ProjId,
                        GroupId = d.GroupId,
                        State = d.State,
                        CreateOn = d.CreateOn,
                        GroupName = dg.Name,
                        EditTime = d.EditTime,
                        IsOSS = d.IsOSS,
                        IsRandomSLD = d.IsRandomSLD,
                        Weight = d.Weight
                    };

            var total = q.LongCount();
            var skip = pageInfo.page > 1 ? (pageInfo.page - 1) * pageInfo.limit : 0;
            var data = q.Skip(skip).Take(pageInfo.limit).ToList();
            return new PageResult<DomainModel> { Data = data, Count = total };
        }

        public DomainModel SelectDomain(int id)
        {
            return Domains.FirstOrDefault(x => x.Id == id);
        }

        public bool InsertDomain(DomainModel domain)
        {
            Domains.Add(domain);
            return SaveChanges() > 0;
        }

        public bool UpdateDomain(DomainModel domain)
        {
            Domains.Update(domain);
            return SaveChanges() > 0;
        }

        public bool DeleteDomain(int id)
        {
            var entity = SelectDomain(id);
            if (entity == null) return false;
            Domains.Remove(entity);
            return SaveChanges() > 0;
        }
    }
}

//----------Domain结束----------

