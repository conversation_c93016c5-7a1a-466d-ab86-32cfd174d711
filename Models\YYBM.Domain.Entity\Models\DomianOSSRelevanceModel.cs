using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Domain.Entity.Models
{
    ///<summary>
    ///DomianOSSRelevance
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-19 16:24:24 
    ///</summary>
    [Table("DomianOSSRelevance")]
    public partial class DomianOSSRelevanceModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(true)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// DomainId
        /// </summary>
        [Required]
        public int DomainId { get; set; }

        /// <summary>
        /// Bucket
        /// </summary>
        [Required]
        public string Bucket { get; set; }

        [Required]
        public int Platform { get; set; }
    }
}

