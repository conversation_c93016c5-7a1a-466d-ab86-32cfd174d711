﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","IServices");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}
#>

//--------------------------------------------------------------------
//     此代码由T4模板自动生成
//	   生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
//     对此文件的更改可能会导致不正确的行为，并且如果重新生成代码，这些更改将会丢失。
//--------------------------------------------------------------------
<# 
	var tableName=config.TableName;
 #>
<# 
if(tableName!=""){

#>  

using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;
using <#=ProjectName#>.Entity.Models;
using <#=ProjectName#>.Entity.ModelParams;
using <#=ProjectName#>.Entity.VOs;

namespace <#=ProjectName#>.IService
{	
	/// <summary>
	/// <#=tableName#>Service
    ///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	/// </summary>	
	public partial interface I<#=tableName#>Service : IBaseService<<#=tableName#>>, I<#=tableName#>Service
    {
	
        I<#=tableName#>Repository dal;
        public <#=tableName#>Service(I<#=tableName#>Repository dal)
        {
            this.dal = dal;
            base.baseDal = dal;
        }
       
    }
}

<# 
} else{ 
#>

<# 
    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
#>

<# 
var primaryKey = "Id";

foreach(System.Data.DataRow row in schema.Rows) 
{  
    var cSharpType = "";   
    var selectMethodParams = "";
	manager.StartBlock("I"+ row["TABLE_NAME"].ToString()+"Service"+".cs",outputPath);
    foreach(DbColumn column in DbHelper.GetDbColumns(config.ConnectionString, config.DbDatabase, row["TABLE_NAME"].ToString()))
    {
         if(column.IsPrimaryKey) 
         {
            cSharpType = column.CSharpType;
            primaryKey = column.ColumnName;
            selectMethodParams = "("+ cSharpType +" "+ primaryKey +")";
         }
     }
    var tableNameModel = row["TABLE_NAME"].ToString()+ "Model";
    var camelCaseName = row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1);
    var interfaceName = "I" + row["TABLE_NAME"].ToString() + "Service";
    var tableListMethod = "";
    var insertMethod = "Add" + row["TABLE_NAME"].ToString() + "(";
    var updateMethod = "Update" + row["TABLE_NAME"].ToString() + "(";
    var deleteMethod = "Delete" + row["TABLE_NAME"].ToString();
    var voName = row["TABLE_NAME"].ToString() + "VO";
    var paramsName = row["TABLE_NAME"].ToString() + "Params";
    var iRepositoryName = "I" + row["TABLE_NAME"].ToString() + "Repository";
    var repositoryName =camelCaseName + "Repository";
    var constructorName = string.Format("public {0}(I{1}Repository {2})", row["TABLE_NAME"].ToString()+"Service", row["TABLE_NAME"].ToString(), repositoryName);
	#>
//----------<#=row["TABLE_NAME"].ToString()#>开始----------
    
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;
using <#=ProjectName#>.Entity.Models;
using <#=ProjectName#>.Entity.ModelParams;
using <#=ProjectName#>.Entity.VOs;

namespace <#=ProjectName#>.IService
{	
	/// <summary>
	/// <#=row["TABLE_NAME"].ToString()#>Service
    ///  此代码由T4模板自动生成
	///	 生成时间 <#=DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")#> 
	/// </summary>	
	public interface I<#=row["TABLE_NAME"].ToString()#>Service : IBaseService<<#=tableNameModel#>>
    {
	
        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<<#=voName#>> GetTableList(<#=paramsName#> searchParams);
        <# if(cSharpType != "int"){ #>

        /// <summary>
        /// 查询
        /// </summary>
        <#=voName#>  GetDetail<#=selectMethodParams#>;
        <#} else {#>

        /// <summary>
        /// 详情
        /// </summary>
        <#=voName#> GetDetail(int Id);
        <#}#>

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult <#=insertMethod#><#=paramsName#> <#=camelCaseName#>Params);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult <#=updateMethod#><#=paramsName#> <#=camelCaseName#>Params);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult <#=deleteMethod#>(<#=paramsName#> <#=camelCaseName#>Params);
       
    }
}

//----------<#=row["TABLE_NAME"].ToString()#>结束----------
<# 
	manager.EndBlock(); 
	} 
	manager.Process(true);
}
#> 