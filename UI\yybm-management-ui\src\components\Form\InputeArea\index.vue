<template>
  <div>
    <el-input v-model="range.min" size="mini">
      <template slot="append">{{ unit }}</template>
    </el-input>
    ~
    <el-input v-model="range.max" size="mini">
      <template slot="append">{{ unit }}</template>
    </el-input>
  </div>
</template>

<script>
export default {
  inheritAttrs: false,
  props: {
    range: {
      type: Object,
      default: () => ({
        min: 0,
        max: 10000,
      }),
    },
    unit: {
      type: String,
      default: '元',
    },
  },
  data() {
    return {
      rangeValue: this.range,
    }
  },
  mounted() {},
  watch: {},
  methods: {},
}
</script>

<style>
.el-input {
  width: 130px;
}
</style>
