<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @cancel="visible = false"
    width="1200px"
  >
    <el-tabs>
      <el-tab-pane label="Entity.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.entity, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.entity }} </pre>
      </el-tab-pane>
      <el-tab-pane label="Table.md">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.tableMd, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.tableMd }} </pre>
      </el-tab-pane>
      <el-tab-pane label="ResponseVO.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.responseVO, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.responseVO }} </pre>
      </el-tab-pane>
      <el-tab-pane label="RequestParams.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.requestParams, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.requestParams }} </pre>
      </el-tab-pane>
      <el-tab-pane label="IRepository.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.iRepository, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.iRepository }} </pre>
      </el-tab-pane>
      <el-tab-pane label="Repository.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.repository, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.repository }} </pre>
      </el-tab-pane>
      <el-tab-pane label="IService.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.iService, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.iService }} </pre>
      </el-tab-pane>
      <el-tab-pane label="Service.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.service, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.service }} </pre>
      </el-tab-pane>
      <el-tab-pane label="Controller.cs">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.controller, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.controller }} </pre>
      </el-tab-pane>
      <el-tab-pane label="api.js">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.api, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.api }} </pre>
      </el-tab-pane>
      <el-tab-pane label="index.vue">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.index, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.index }} </pre>
      </el-tab-pane>
      <el-tab-pane label="Edit.vue">
        <el-button
          class="copy-btn"
          type="text"
          icon="el-icon-document-copy"
          @click="handleCopy(data.edit, $event)"
          >复制代码</el-button
        >
        <pre> {{ data.edit }} </pre>
      </el-tab-pane>
    </el-tabs>
  </x-dialog>
</template>

<script>
import clip from '@/utils/clipboard'
import { genApi } from '@/api'
export default {
  data() {
    return {
      title: '预览',
      visible: false,
      loading: false,
      data: {},
    }
  },
  methods: {
    async open(params) {
      this.data = {}
      this.visible = true
      this.$xloading.show()
      const res = await genApi.getCodePreview(params)
      this.$xloading.hide()
      this.data = res.data
      // this.data.entity = this.replaceEnter(entity)
    },
    replaceEnter(content) {
      let reg = new RegExp('\r\n', 'g')
      return content.replace(reg, '<br/>')
    },
    handleCopy(text, event) {
      clip(text, event)
    },
  },
}
</script>

<style lang="scss" scoped>
.copy-btn {
  float: right;
}
</style>
