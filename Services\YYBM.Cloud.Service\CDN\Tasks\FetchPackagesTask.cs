﻿using TencentCloud.Apis.CDN;
using YYBM.Cloud.Entity.Models;
using YYBM.Cloud.IRepository;
using ZProjectBase.Common.Ioc;
using ZProjectBase.Common.ITask;

namespace YYBM.Cloud.Service.CDN.Tasks
{
    public class FetchPackagesTask : ITaskAsync
    {
        private readonly IAccountRepository _accountRepository = HttpContextServiceLocator.GetService<IAccountRepository>();
        private readonly IHttpspackageRepository _httpspackageRepository = HttpContextServiceLocator.GetService<IHttpspackageRepository>();
        private readonly ITrafficpackageRepository _trafficpackageRepository = HttpContextServiceLocator.GetService<ITrafficpackageRepository>();

        public async Task StartAsync()
        {
            var accounts = await _accountRepository.GetAllAsync();
            if (accounts.Count() == 0)
                return;

            await FetchHttpsPackages(accounts);
            await FetchTrafficPackages(accounts);
        }

        private async Task FetchHttpsPackages(IEnumerable<AccountModel> accounts)
        {
            foreach (var account in accounts)
            {
                var describeHttpsPackagesApi = new DescribeHttpsPackagesApi();
                var res = await describeHttpsPackagesApi.GetDescribeHttpsPackages(account.SecretId, account.SecretKey);
                if (res.Response == null || res.Response.HttpsPackages == null)
                    continue;

                var enabledPackages = res.Response.HttpsPackages
                    .Where(o => o.Status == "enabled" && o.Size > o.SizeUsed)
                    .ToList();

                var size = enabledPackages.Sum(o => o.Size - o.SizeUsed);
                var sizeUsed = enabledPackages.Sum(o => o.SizeUsed);

                var httpspackage = await _httpspackageRepository.SelectAsyncByAccount(account.Id);
                if (httpspackage == null)
                {
                    httpspackage = new HttpspackageModel()
                    {
                        Platform = account.Platform,
                        AccountId = account.Id,
                        Size = size,
                        SizeUsed = sizeUsed,
                        LastUpdateTime = DateTime.Now,
                        EnableCount = res.Response.EnabledCount
                    };
                    await _httpspackageRepository.CreateAsync(httpspackage);
                }
                else
                {
                    httpspackage.Size = size;
                    httpspackage.SizeUsed = sizeUsed;
                    httpspackage.LastUpdateTime = DateTime.Now;
                    httpspackage.EnableCount = res.Response.EnabledCount;
                    await _httpspackageRepository.UpdateAsync(httpspackage);
                }
            }
        }

        private async Task FetchTrafficPackages(IEnumerable<AccountModel> accounts)
        {
            foreach (var account in accounts)
            {
                var describeTrafficPackagesApi = new DescribeTrafficPackagesApi();
                var res = await describeTrafficPackagesApi.GetDescribeTrafficPackages(account.SecretId, account.SecretKey);
                if (res.Response == null || res.Response.TrafficPackages == null)
                    continue;

                var enabledPackages = res.Response.TrafficPackages
                    .Where(o => o.Status == "enabled" && o.Bytes > o.BytesUsed)
                    .ToList();

                var gb = enabledPackages.Sum(o => o.Bytes - o.BytesUsed) / **********;
                var gbUsed = enabledPackages.Sum(o => o.BytesUsed) / **********;

                var trafficpackage = await _trafficpackageRepository.SelectAsyncByAccount(account.Id);
                if (trafficpackage == null)
                {
                    trafficpackage = new TrafficpackageModel()
                    {
                        Platform = account.Platform,
                        AccountId = account.Id,
                        GB = gb,
                        GBUsed = gbUsed,
                        LastUpdateTime = DateTime.Now,
                        EnableCount = res.Response.EnabledCount
                    };
                    await _trafficpackageRepository.CreateAsync(trafficpackage);
                }
                else
                {
                    trafficpackage.GB = gb;
                    trafficpackage.GBUsed = gbUsed;
                    trafficpackage.LastUpdateTime = DateTime.Now;
                    trafficpackage.EnableCount = res.Response.EnabledCount;
                    await _trafficpackageRepository.UpdateAsync(trafficpackage);
                }
            }
        }
    }
}
