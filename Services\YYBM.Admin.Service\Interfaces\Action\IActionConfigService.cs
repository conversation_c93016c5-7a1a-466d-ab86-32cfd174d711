﻿using YYBM.Entity.ModelParams;
using YYBM.Entity.Models;
using YYBM.Entity.VOs;
using ZProjectBase.DB.IService;
using ZProjectBase.Mvc;

namespace YYBM.Admin.IService
{
    /// <summary>
    /// ActionConfigService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-07-18 15:31:41 
    /// </summary>	
    public interface IActionConfigService : IBaseService<ActionConfigModel>
    {

        /// <summary>
        /// 列表查询
        /// </summary>
        TableResult<ActionConfigVO> GetTableList(ActionConfigParams searchParams);

        /// <summary>
        /// 查询
        /// </summary>
        ActionConfigVO GetDetail(string ActionFlag);

        /// <summary>
        /// 新增
        /// </summary>
        ResponseResult AddActionConfig(ActionConfigParams actionConfigParams);

        /// <summary>
        /// 修改
        /// </summary>
        ResponseResult UpdateActionConfig(ActionConfigParams actionConfigParams);

        /// <summary>
        /// 删除
        /// </summary>
        ResponseResult DeleteActionConfig(ActionConfigParams actionConfigParams);

    }
}
