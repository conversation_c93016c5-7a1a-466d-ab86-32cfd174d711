﻿<#@ template debug="false" hostspecific="true" language="C#" #>
<#@ output extension="/" #>
<#@ assembly name="System.Core.dll" #>
<#@ assembly name="System.Data.dll" #>
<#@ assembly name="System.Data.DataSetExtensions.dll" #>
<#@ assembly name="System.Xml.dll" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Xml" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ import namespace="System.IO" #>
<#@ include file="$(ProjectDir)DbHelper.ttinclude"  #>
<#@ include file="$(ProjectDir)Model.ttinclude"	#>
<#@ include file="$(ProjectDir)Global.ttinclude"  #>
<# var manager = new Manager(Host, GenerationEnvironment, true); #>

<# 
	var outputPath =Path.GetDirectoryName(Path.GetDirectoryName(Path.GetDirectoryName(Host.TemplateFile+"..")+"..")+"..");
	outputPath=Path.Combine(outputPath,OutputDllPath,"Temp","VueApi");
	if (!Directory.Exists(outputPath))
	{
	    Directory.CreateDirectory(outputPath);
	}

    SqlConnection conn = new SqlConnection(config.ConnectionString); 
    conn.Open(); 
    System.Data.DataTable schema = conn.GetSchema("TABLES"); 
    foreach(System.Data.DataRow row in schema.Rows) 
    {  
        manager.StartBlock(row["TABLE_NAME"].ToString()+"VueApi"+".js",outputPath);
        var camelCaseName = row["TABLE_NAME"].ToString()[0].ToString().ToLower() + row["TABLE_NAME"].ToString().Substring(1);
        var tableName = row["TABLE_NAME"].ToString();
#>
import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/<#=camelCaseName#>/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/<#=camelCaseName#>/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function add<#=tableName#>(data) {
  return request({
    url: '/<#=camelCaseName#>/add',
    method: 'post',
    data: data,
  })
}

export function edit<#=tableName#>(data) {
  return request({
    url: '/<#=camelCaseName#>/edit',
    method: 'post',
    data: data,
  })
}

export function del<#=tableName#>(id) {
  return request({
    url: '/<#=camelCaseName#>/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

//----------<#=row["TABLE_NAME"].ToString()#>结束----------
<# 
	    manager.EndBlock(); 
    }
	manager.Process(true);

#> 