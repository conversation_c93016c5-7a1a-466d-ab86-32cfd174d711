<template>
  <div class="app-container">
    <el-alert title="说明" type="warning" :closable="false">
      <div>
        用于生成数据库表对应的实体类、后台CRUD代码
      </div>
    </el-alert>
    <el-card class="mt-20">
      <el-tabs v-model="tabName" @tab-click="handleTabClick">
        <el-tab-pane v-for="item in dbList" :key="item.value" :label="item.label"></el-tab-pane>
      </el-tabs>

      <x-table
        :autoLoad="false"
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="tableName" label="表名"></el-table-column>
        <el-table-column prop="tableComment" label="表描述"></el-table-column>
        <el-table-column label="操作" width="160" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handlePreview(scope.row)">预览</el-button>
            <el-button size="mini" type="text" @click="handleDownload(scope.row)">下载</el-button>
          </template>
        </el-table-column>
      </x-table>
    </el-card>
    <preview-dialog ref="preview"></preview-dialog>
  </div>
</template>

<script>
import PreviewDialog from './PreviewDialog'
import { genApi, optionsApi } from '@/api'
import { tableHeightMixin } from '@/mixin'
import { downLoadZip } from '@/utils/zipdownload'

export default {
  components: {
    PreviewDialog,
  },
  mixins: [],
  data() {
    return {
      loading: false,
      tableData: {},
      queryParams: {},
      tabName: '',
      dbList: [],
      canDownload: true,
    }
  },
  async mounted() {
    await this.loadData()
    this.reloadByDbIndex(0)
  },
  methods: {
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await genApi.getTableList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    async loadData() {
      const dbRes = await optionsApi.getDatabase()
      this.dbList = dbRes.data
    },
    reloadByDbIndex(index) {
      this.queryParams.database = this.dbList[index].value
      this.$refs.table.refresh(true)
    },
    handleTabClick(tab) {
      this.reloadByDbIndex(tab.index)
    },

    async handlePreview(row) {
      const params = {
        tableName: row.tableName,
        database: this.queryParams.database,
      }
      this.$refs.preview.open(params)
    },
    async handleDownload(row) {
      if (!this.canDownload) {
        this.$xMsgError('操作太快了，稍候再试~')
        return
      }
      const params = {
        tableName: row.tableName,
        database: this.queryParams.database,
      }
      const url = `/system/gen/download?tableName=${row.tableName}&database=${this.queryParams.database}`
      downLoadZip(true, url)
      // 开启定时器，不给操作那么快
      this.canDownload = false
      setTimeout(() => {
        this.canDownload = true
      }, 6000)
    },
  },
}
</script>

<style></style>
