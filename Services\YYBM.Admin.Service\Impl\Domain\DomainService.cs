//----------Domain开始----------

using Microsoft.AspNetCore.Http;
using YYBM.Admin.IService;
using YYBM.Domain.Entity.ModelParams;
using YYBM.Domain.Entity.Models;
using YYBM.Domain.Entity.VOs;
using YYBM.Domain.IRepository;
using YYBM.IRepository;
using ZProjectBase.Admin.Core;
using ZProjectBase.Common.Exceptions;
using ZProjectBase.Common.Utils;
using ZProjectBase.Mvc;

namespace YYBM.Admin.Service
{
    /// <summary>
    /// DomainService
    ///  此代码由T4模板自动生成
    ///	 生成时间 2025-08-15 19:38:56 
    /// </summary>	
    public class DomainService : AdminServiceBase<DomainModel>, IDomainService
    {

        private readonly IDomainRepository _domainRepository;
        private readonly IDomainGroupRepository _domainGroupRepository;
        private readonly IProjectConfigRepository _projectConfigRepository;

        public DomainService(
            IHttpContextAccessor httpContextAccessor,
            IDomainRepository domainRepository,
            IDomainGroupRepository domainGroupRepository,
            IProjectConfigRepository projectConfigRepository) : base(httpContextAccessor)
        {
            _domainRepository = domainRepository;
            _domainGroupRepository = domainGroupRepository;
            _projectConfigRepository = projectConfigRepository;
        }

        /// <summary>
        /// 列表查询
        /// </summary>
        public TableResult<DomainVO> GetTableList(DomainParams searchParams)
        {
            var pageResult = _domainRepository.SelectPage(searchParams, searchParams.ToPageInfo());
            var tableResult = CreateTableResult<DomainModel, DomainVO>(pageResult);
            foreach (var item in tableResult.Data)
            {
                item.GroupName = _domainGroupRepository.Select(item.GroupId)?.Name ?? "未分组";
                item.ProjName = _projectConfigRepository.Select(item.ProjId)?.Name ?? "未配置项目";
            }
            return tableResult;
        }


        /// <summary>
        /// 详情
        /// </summary>
        public DomainVO GetDetail(int Id)
        {
            var domain = _domainRepository.Select(Id);
            if (domain == null)
            {
                throw new BusinessException("数据不存在");
            }
            var domainVO = MapperIns.Map<DomainVO>(domain);
            return domainVO;
        }

        /// <summary>
        /// 新增
        /// </summary>
        public ResponseResult AddDomain(DomainParams domainParams)
        {
            var domain = MapperIns.Map<DomainModel>(domainParams);
            domain.CreateOn = DateTime.Now;
            var result = _domainRepository.InsertDomain(domain);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public ResponseResult UpdateDomain(DomainParams domainParams)
        {
            var domain = MapperIns.Map<DomainModel>(domainParams);
            domain.EditTime = DateTime.Now;
            var result = _domainRepository.UpdateDomain(domain);
            return ResponseResult.Result(result);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public ResponseResult DeleteDomain(DomainParams domainParams)
        {
            CheckParamsUtils.Failure(domainParams.Id == 0, "Id不能为空");

            var result = _domainRepository.DeleteDomain(domainParams.Id);
            return ResponseResult.Result(result);
        }

    }
}

//----------Domain结束----------
