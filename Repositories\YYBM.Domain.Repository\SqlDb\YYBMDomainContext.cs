﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YYBM.Domain.Entity.Models;
using ZProjectBase.DB.EF.Repository;

namespace YYBM.Domain.Repository.SqlDb
{
    public class YYBMDomainContext<T> : EFBaseRepository<T> where T : class, new()
    {
        public DbSet<DomainModel> Domains { get; set; }
        public DbSet<DomainGroupModel> DomainGroups { get; set; }

        public YYBMDomainContext(DbContextOptions options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<DomainModel>(entity =>
            {
                entity.ToTable("Domain");

                entity.HasKey(e => e.Id);
            });

            modelBuilder.Entity<DomainGroupModel>(entity =>
            {
                entity.ToTable("DomainGroup");

                entity.HasKey(e => e.Id);
            });
        }
    }
}
