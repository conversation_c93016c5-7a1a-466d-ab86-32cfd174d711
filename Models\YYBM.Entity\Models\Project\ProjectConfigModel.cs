using System.ComponentModel.DataAnnotations;
using ZProjectBase.DB.DapperExt;

namespace YYBM.Entity.Models
{
    ///<summary>
    ///ProjectConfig
    ///  此代码由T4模板自动生成
    ///  生成时间 2025-08-15 18:12:07 
    ///</summary>
    [Table("ProjectConfig")]
    public partial class ProjectConfigModel
    {

        /// <summary>
        /// Id
        /// </summary>
        [ZProjectBase.DB.DapperExt.Key(false)]
        [Required]
        public int Id { get; set; }

        /// <summary>
        /// ParentId
        /// </summary>
        [Required]
        public int ParentId { get; set; }

        /// <summary>
        /// Symbol
        /// </summary>
        [Required]
        public string Symbol { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        [Required]
        public string Name { get; set; }

        /// <summary>
        /// CreateOn
        /// </summary>
        [Required]
        public DateTime CreateOn { get; set; }

        /// <summary>
        /// EditTime
        /// </summary>
        public DateTime? EditTime { get; set; }

    }
}

