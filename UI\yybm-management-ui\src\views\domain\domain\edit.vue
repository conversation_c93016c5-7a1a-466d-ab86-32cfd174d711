<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
    width="800px"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="medium"
      class="domain-form"
    >
      <el-row :gutter="20">
        <!-- 基本信息 -->
        <el-col :span="24">
          <div class="form-section-title">
            <i class="el-icon-setting"></i>
            基本信息
          </div>
        </el-col>

        <el-col :span="24">
          <el-form-item label="域名ID" prop="id">
            <el-input
              :disabled="true"
              v-model="form.id"
              placeholder="系统自动生成唯一ID"
              class="id-input"
            >
              <template slot="prepend">
                <i class="el-icon-key"></i>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="所属项目" prop="projId">
            <x-select
              v-model="form.projId"
              url="/options/getProjectOptions"
              placeholder="选择所属项目"
              style="width: 100%"
              customRender
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="所属分组" prop="groupId">
            <x-select
              v-model="form.groupId"
              url="/options/getDomainGroupOptions"
              placeholder="选择域名分组"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="域名地址" prop="domain">
            <el-input v-model="form.domain" placeholder="请输入完整域名，如：example.com" clearable>
              <template slot="prepend">
                <i class="el-icon-link"></i>
              </template>
            </el-input>
            <div class="form-tip">
              <i class="el-icon-info"></i>
              请输入有效的域名地址
            </div>
          </el-form-item>
        </el-col>

        <!-- 配置信息 -->
        <el-col :span="24">
          <div class="form-section-title">
            <i class="el-icon-s-tools"></i>
            配置信息
          </div>
        </el-col>

        <el-col :span="12">
          <el-form-item label="权重设置" prop="weight">
            <el-input-number
              v-model="form.weight"
              placeholder="权重值"
              :min="1"
              :max="999"
              controls-position="right"
              style="width: 100%"
            />
            <div class="form-tip-inline">
              <i class="el-icon-scale-to-original"></i>
              权重越高，被选中的概率越大
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="域名状态" prop="state">
            <div class="radio-group-container">
              <x-radio v-model="form.state" button :options="stateOptions" />
            </div>
            <div class="form-tip-inline">
              <i class="el-icon-info"></i>
              控制域名的启用状态
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="二级域名" prop="isRandomSLD">
            <div class="radio-group-container">
              <x-radio v-model="form.isRandomSLD" button :options="randomSLDOptions" />
            </div>
            <div class="form-tip">
              <i class="el-icon-info"></i>
              <span>
                <strong>随机：</strong>系统自动生成随机二级域名（如：abc123.example.com）<br />
                <strong>固定：</strong>使用固定的二级域名或直接使用主域名
              </span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { domainStatusOptions } from '@/utils/constant'
import { domainApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {
        isRandomSLD: false,
        state: 1,
        weight: 10,
      },
      randomSLDOptions: [
        { label: '随机', value: true },
        { label: '固定', value: false },
      ],
      rules: {
        domain: [{ required: true, message: '请输入域名', trigger: 'blur' }],
        projId: [{ required: true, message: '请选择项目', trigger: 'blur' }],
        groupId: [{ required: true, message: '请选择分组', trigger: 'blur' }],
        weight: [{ required: true, message: '请输入权重', trigger: 'blur' }],
        state: [{ required: true, message: '请选择状态', trigger: 'blur' }],
      },
      domainStatusOptions,
    }
  },
  computed: {
    stateOptions() {
      return this.domainStatusOptions.filter((t) => t.value != null)
    },
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await domainApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        isRandomSLD: false,
        state: 1,
        weight: 10,
      }
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await domainApi.editDomain(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await domainApi.addDomain(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.domain-form {
  padding: 0 8px;
}

.form-section-title {
  display: flex;
  align-items: center;
  padding: 12px 0 16px 0;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #f0f0f0;
}

.form-section-title i {
  margin-right: 8px;
  color: #409eff;
  font-size: 18px;
}

.form-tip {
  display: flex;
  align-items: flex-start;
  margin-top: 8px;
  padding: 10px 14px;
  background: #f8f9fa;
  border-left: 3px solid #409eff;
  border-radius: 4px;
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
}

.form-tip i {
  margin-right: 6px;
  margin-top: 1px;
  color: #409eff;
  font-size: 14px;
  flex-shrink: 0;
}

.form-tip-inline {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.form-tip-inline i {
  margin-right: 4px;
  font-size: 13px;
}

.id-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.radio-group-container {
  margin-bottom: 4px;
}

/* 输入框前置图标样式 */
.el-input-group__prepend {
  background: #fafafa;
  border-color: #dcdfe6;
  color: #909399;
}

/* 表单项间距优化 */
.domain-form .el-form-item {
  margin-bottom: 24px;
}

/* 输入框样式优化 */
.domain-form .el-input__inner {
  border-radius: 6px;
  border-color: #dcdfe6;
  transition: all 0.3s;
}

.domain-form .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 数字输入框样式优化 */
.domain-form .el-input-number {
  width: 100%;
}

.domain-form .el-input-number .el-input__inner {
  text-align: left;
  padding-right: 50px;
}

.domain-form .el-input-number__increase,
.domain-form .el-input-number__decrease {
  background: #fafafa;
  border-color: #dcdfe6;
}

.domain-form .el-input-number__increase:hover,
.domain-form .el-input-number__decrease:hover {
  background: #409eff;
  color: #fff;
}

/* 选择框样式 */
.domain-form .el-select .el-input__inner {
  cursor: pointer;
}

/* 单选按钮组样式优化 */
.domain-form .el-radio-button__inner {
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: all 0.3s;
}

.domain-form .el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background: #409eff;
  border-color: #409eff;
  box-shadow: -1px 0 0 0 #409eff;
}

/* 标签样式优化 */
.domain-form .el-form-item__label {
  font-weight: 500;
  color: #303133;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .form-section-title {
    font-size: 14px;
  }

  .domain-form {
    padding: 0;
  }

  .domain-form .el-form-item__label {
    width: 100px !important;
  }
}
</style>
